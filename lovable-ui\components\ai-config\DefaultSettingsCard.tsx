"use client";

import { useState } from 'react';
import { useAIConfig } from '@/lib/contexts/ai-config-context';
import { AVAILABLE_MODELS } from '@/lib/types/ai-config';

export default function DefaultSettingsCard() {
  const { state, actions } = useAIConfig();
  const [isEditing, setIsEditing] = useState(false);
  const [tempParams, setTempParams] = useState(state.config.defaultParameters);

  const handleSave = () => {
    actions.updateDefaultParameters(tempParams);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setTempParams(state.config.defaultParameters);
    setIsEditing(false);
  };

  const getAvailableModels = () => {
    const enabledProviders = state.config.providers.filter(p => p.enabled);
    const models: Array<{ id: string; name: string; provider: string }> = [];
    
    enabledProviders.forEach(provider => {
      const providerModels = AVAILABLE_MODELS[provider.provider] || [];
      providerModels.forEach(model => {
        models.push({
          id: model.id,
          name: `${model.name} (${provider.name})`,
          provider: provider.id,
        });
      });
    });

    return models;
  };

  const availableModels = getAvailableModels();
  const defaultProvider = state.config.providers.find(p => p.id === state.config.defaultProvider);

  return (
    <div className="bg-gray-900 rounded-lg border border-gray-800 p-6 mb-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-white mb-1">Default Settings</h2>
          <p className="text-gray-400 text-sm">
            Configure default AI model and parameters for code generation.
          </p>
        </div>
        <button
          onClick={() => setIsEditing(!isEditing)}
          className="text-gray-400 hover:text-white transition-colors"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Default Provider & Model */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-white">Provider & Model</h3>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Default Provider
            </label>
            <select
              value={state.config.defaultProvider || ''}
              onChange={(e) => actions.setDefaultProvider(e.target.value)}
              className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={state.config.providers.length === 0}
            >
              <option value="">Select a provider</option>
              {state.config.providers.filter(p => p.enabled).map(provider => (
                <option key={provider.id} value={provider.id}>
                  {provider.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Default Model
            </label>
            <select
              value={state.config.defaultModel || ''}
              onChange={(e) => actions.setDefaultModel(e.target.value)}
              className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={availableModels.length === 0}
            >
              <option value="">Select a model</option>
              {availableModels.map(model => (
                <option key={`${model.provider}-${model.id}`} value={model.id}>
                  {model.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Model Parameters */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-white">Model Parameters</h3>
          
          {isEditing ? (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Temperature ({tempParams.temperature})
                </label>
                <input
                  type="range"
                  min="0"
                  max="2"
                  step="0.1"
                  value={tempParams.temperature}
                  onChange={(e) => setTempParams(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Max Tokens
                </label>
                <input
                  type="number"
                  min="1"
                  max="32000"
                  value={tempParams.maxTokens}
                  onChange={(e) => setTempParams(prev => ({ ...prev, maxTokens: parseInt(e.target.value) }))}
                  className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Top P ({tempParams.topP})
                </label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={tempParams.topP}
                  onChange={(e) => setTempParams(prev => ({ ...prev, topP: parseFloat(e.target.value) }))}
                  className="w-full"
                />
              </div>

              <div className="flex space-x-2">
                <button
                  onClick={handleSave}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  Save
                </button>
                <button
                  onClick={handleCancel}
                  className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">Temperature:</span>
                <span className="text-white">{state.config.defaultParameters.temperature}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Max Tokens:</span>
                <span className="text-white">{state.config.defaultParameters.maxTokens}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Top P:</span>
                <span className="text-white">{state.config.defaultParameters.topP}</span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Current Status */}
      <div className="mt-6 pt-6 border-t border-gray-800">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-400">Current Configuration:</span>
          <span className="text-white">
            {defaultProvider ? (
              <>
                {defaultProvider.name}
                {state.config.defaultModel && ` • ${state.config.defaultModel}`}
              </>
            ) : (
              'No default provider set'
            )}
          </span>
        </div>
      </div>
    </div>
  );
}
