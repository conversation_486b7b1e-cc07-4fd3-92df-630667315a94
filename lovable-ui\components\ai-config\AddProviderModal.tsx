"use client";

import { useState, useEffect } from 'react';
import { ProviderConfig, AIProvider } from '@/lib/types/ai-config';

interface AddProviderModalProps {
  provider?: ProviderConfig | null;
  onSave: (provider: ProviderConfig) => void;
  onCancel: () => void;
}

export default function AddProviderModal({ provider, onSave, onCancel }: AddProviderModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    provider: 'openai' as AIProvider,
    apiKey: '',
    baseURL: '',
    organization: '',
    project: '',
    modelName: '',
    headers: {} as Record<string, string>,
    enabled: true,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (provider) {
      setFormData({
        name: provider.name,
        provider: provider.provider,
        apiKey: provider.apiKey,
        baseURL: (provider as any).baseURL || '',
        organization: (provider as any).organization || '',
        project: (provider as any).project || '',
        modelName: (provider as any).modelName || '',
        headers: (provider as any).headers || {},
        enabled: provider.enabled,
      });
    }
  }, [provider]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Provider name is required';
    }

    if (!formData.apiKey.trim()) {
      newErrors.apiKey = 'API key is required';
    }

    if (formData.provider === 'custom') {
      if (!formData.baseURL.trim()) {
        newErrors.baseURL = 'Base URL is required for custom providers';
      }
      if (!formData.modelName.trim()) {
        newErrors.modelName = 'Model name is required for custom providers';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const newProvider: ProviderConfig = {
      id: provider?.id || `${formData.provider}-${Date.now()}`,
      name: formData.name,
      provider: formData.provider,
      apiKey: formData.apiKey,
      enabled: formData.enabled,
      ...(formData.baseURL && { baseURL: formData.baseURL }),
      ...(formData.organization && { organization: formData.organization }),
      ...(formData.project && { project: formData.project }),
      ...(formData.modelName && { modelName: formData.modelName }),
      ...(Object.keys(formData.headers).length > 0 && { headers: formData.headers }),
    } as ProviderConfig;

    onSave(newProvider);
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg border border-gray-800 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-800">
          <h2 className="text-xl font-semibold text-white">
            {provider ? 'Edit Provider' : 'Add AI Provider'}
          </h2>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Provider Type */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Provider Type
            </label>
            <select
              value={formData.provider}
              onChange={(e) => handleInputChange('provider', e.target.value as AIProvider)}
              className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={!!provider} // Disable editing provider type for existing providers
            >
              <option value="openai">OpenAI</option>
              <option value="anthropic">Anthropic</option>
              <option value="google">Google</option>
              <option value="cohere">Cohere</option>
              <option value="mistral">Mistral</option>
              <option value="custom">Custom (OpenAI-compatible)</option>
            </select>
          </div>

          {/* Provider Name */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Provider Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className={`w-full bg-gray-800 border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.name ? 'border-red-500' : 'border-gray-700'
              }`}
              placeholder="e.g., My OpenAI Account"
            />
            {errors.name && <p className="text-red-400 text-sm mt-1">{errors.name}</p>}
          </div>

          {/* API Key */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              API Key
            </label>
            <input
              type="password"
              value={formData.apiKey}
              onChange={(e) => handleInputChange('apiKey', e.target.value)}
              className={`w-full bg-gray-800 border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.apiKey ? 'border-red-500' : 'border-gray-700'
              }`}
              placeholder="Enter your API key"
            />
            {errors.apiKey && <p className="text-red-400 text-sm mt-1">{errors.apiKey}</p>}
          </div>

          {/* Base URL (for custom providers and optional for others) */}
          {(formData.provider === 'custom' || formData.baseURL) && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Base URL {formData.provider === 'custom' && <span className="text-red-400">*</span>}
              </label>
              <input
                type="url"
                value={formData.baseURL}
                onChange={(e) => handleInputChange('baseURL', e.target.value)}
                className={`w-full bg-gray-800 border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.baseURL ? 'border-red-500' : 'border-gray-700'
                }`}
                placeholder="https://api.example.com/v1"
              />
              {errors.baseURL && <p className="text-red-400 text-sm mt-1">{errors.baseURL}</p>}
            </div>
          )}

          {/* Custom Model Name (for custom providers) */}
          {formData.provider === 'custom' && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Model Name <span className="text-red-400">*</span>
              </label>
              <input
                type="text"
                value={formData.modelName}
                onChange={(e) => handleInputChange('modelName', e.target.value)}
                className={`w-full bg-gray-800 border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.modelName ? 'border-red-500' : 'border-gray-700'
                }`}
                placeholder="e.g., gpt-3.5-turbo"
              />
              {errors.modelName && <p className="text-red-400 text-sm mt-1">{errors.modelName}</p>}
            </div>
          )}

          {/* OpenAI specific fields */}
          {formData.provider === 'openai' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Organization ID (Optional)
                </label>
                <input
                  type="text"
                  value={formData.organization}
                  onChange={(e) => handleInputChange('organization', e.target.value)}
                  className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="org-..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Project ID (Optional)
                </label>
                <input
                  type="text"
                  value={formData.project}
                  onChange={(e) => handleInputChange('project', e.target.value)}
                  className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="proj_..."
                />
              </div>
            </>
          )}

          {/* Enable/Disable Toggle */}
          <div className="flex items-center justify-between">
            <label className="block text-sm font-medium text-gray-300">
              Enable Provider
            </label>
            <button
              type="button"
              onClick={() => handleInputChange('enabled', !formData.enabled)}
              className={`w-12 h-6 rounded-full transition-colors ${
                formData.enabled ? 'bg-green-600' : 'bg-gray-600'
              }`}
            >
              <div
                className={`w-5 h-5 bg-white rounded-full transition-transform ${
                  formData.enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-800">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
            >
              {provider ? 'Update Provider' : 'Add Provider'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
