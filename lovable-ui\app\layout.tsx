import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AIConfigProvider } from "@/lib/contexts/ai-config-context";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Lovable Clone - AI-Powered Code Generation",
  description: "Build applications faster with AI-powered code generation",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AIConfigProvider>
          {children}
        </AIConfigProvider>
      </body>
    </html>
  );
}