import { NextRequest, NextResponse } from 'next/server';
import { AIProviderService } from '@/lib/services/ai-provider-service';
import { ProviderConfig } from '@/lib/types/ai-config';

export async function POST(req: NextRequest) {
  try {
    const { provider }: { provider: ProviderConfig } = await req.json();

    if (!provider) {
      return NextResponse.json(
        { success: false, error: 'Provider configuration is required' },
        { status: 400 }
      );
    }

    const aiService = AIProviderService.getInstance();
    const result = await aiService.testConnection(provider);

    return NextResponse.json(result);
  } catch (error) {
    console.error('Connection test error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    );
  }
}
