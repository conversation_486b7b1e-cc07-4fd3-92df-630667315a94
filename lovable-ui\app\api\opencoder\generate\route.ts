import { NextRequest, NextResponse } from 'next/server';
import { OpencoderService } from '@/lib/services/opencoder-service';
import { AIConfigService } from '@/lib/services/ai-config-service';
import { ProviderConfig, ModelParameters } from '@/lib/types/ai-config';

export async function POST(req: NextRequest) {
  try {
    const { 
      prompt, 
      providerId, 
      modelId, 
      parameters, 
      systemPrompt,
      workingDirectory,
      stream = true 
    } = await req.json();

    if (!prompt?.trim()) {
      return NextResponse.json(
        { success: false, error: 'Prompt is required' },
        { status: 400 }
      );
    }

    console.log('[Opencoder API] Starting code generation for prompt:', prompt);

    const configService = AIConfigService.getInstance();
    const opencoderService = OpencoderService.getInstance();

    // Determine which provider and model to use
    let provider: ProviderConfig;
    let model: string;

    if (providerId && modelId) {
      // Use specified provider and model
      const specifiedProvider = configService.getProvider(providerId);
      if (!specifiedProvider) {
        return NextResponse.json(
          { success: false, error: `Provider with ID ${providerId} not found` },
          { status: 400 }
        );
      }
      provider = specifiedProvider;
      model = modelId;
    } else {
      // Use default provider and model
      const defaultProvider = configService.getDefaultProvider();
      const defaultModel = configService.getDefaultModel();
      
      if (!defaultProvider || !defaultModel) {
        return NextResponse.json(
          { success: false, error: 'No default AI provider or model configured. Please configure a provider in settings.' },
          { status: 400 }
        );
      }
      
      provider = defaultProvider;
      model = defaultModel;
    }

    console.log(`[Opencoder API] Using provider: ${provider.name}, model: ${model}`);

    // Merge parameters with defaults
    const finalParameters: ModelParameters = {
      ...configService.getConfig().defaultParameters,
      ...parameters,
    };

    if (stream) {
      // Handle streaming response
      const result = await opencoderService.generateCode({
        prompt,
        provider,
        modelId: model,
        parameters: finalParameters,
        systemPrompt,
        workingDirectory,
      });

      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.error },
          { status: 500 }
        );
      }

      // Create streaming response
      const encoder = new TextEncoder();
      const stream = new TransformStream();
      const writer = stream.writable.getWriter();

      // Start streaming in the background
      (async () => {
        try {
          // Send metadata first
          await writer.write(
            encoder.encode(`data: ${JSON.stringify({
              type: 'metadata',
              provider: provider.name,
              model: model,
              timestamp: new Date().toISOString(),
            })}\n\n`)
          );

          // Send each message from opencoder
          if (result.messages) {
            for (const message of result.messages) {
              await writer.write(
                encoder.encode(`data: ${JSON.stringify(message)}\n\n`)
              );
              
              // Add a small delay to simulate real-time streaming
              await new Promise(resolve => setTimeout(resolve, 50));
            }
          }

          // Send usage information
          if (result.usage) {
            await writer.write(
              encoder.encode(`data: ${JSON.stringify({
                type: 'usage',
                usage: result.usage,
                timestamp: new Date().toISOString(),
              })}\n\n`)
            );
          }

          // Send completion signal
          await writer.write(
            encoder.encode(`data: ${JSON.stringify({
              type: 'complete',
              timestamp: new Date().toISOString(),
            })}\n\n`)
          );
        } catch (error) {
          console.error('[Opencoder API] Streaming error:', error);
          await writer.write(
            encoder.encode(`data: ${JSON.stringify({
              type: 'error',
              error: error instanceof Error ? error.message : 'Streaming failed',
              timestamp: new Date().toISOString(),
            })}\n\n`)
          );
        } finally {
          await writer.close();
        }
      })();

      return new Response(stream.readable, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      });
    } else {
      // Handle non-streaming response
      const result = await opencoderService.generateCode({
        prompt,
        provider,
        modelId: model,
        parameters: finalParameters,
        systemPrompt,
        workingDirectory,
      });

      return NextResponse.json({
        success: result.success,
        messages: result.messages,
        error: result.error,
        usage: result.usage,
        metadata: {
          provider: provider.name,
          model: model,
          timestamp: new Date().toISOString(),
        },
      });
    }
  } catch (error) {
    console.error('[Opencoder API] Generation error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
