import { NextRequest, NextResponse } from 'next/server';
import { AIProviderService } from '@/lib/services/ai-provider-service';
import { ProviderConfig, GenerationRequest, ModelParameters } from '@/lib/types/ai-config';

export async function POST(req: NextRequest) {
  try {
    const body: GenerationRequest & { 
      provider: ProviderConfig;
      modelId: string;
    } = await req.json();

    const { prompt, provider, modelId, parameters, stream } = body;

    if (!prompt) {
      return NextResponse.json(
        { success: false, error: 'Prompt is required' },
        { status: 400 }
      );
    }

    if (!provider) {
      return NextResponse.json(
        { success: false, error: 'Provider configuration is required' },
        { status: 400 }
      );
    }

    if (!modelId) {
      return NextResponse.json(
        { success: false, error: 'Model ID is required' },
        { status: 400 }
      );
    }

    console.log(`[AI API] Starting generation with ${provider.name} (${provider.provider}) - Model: ${modelId}`);

    const aiService = AIProviderService.getInstance();

    if (stream) {
      // Handle streaming response
      const streamResult = await aiService.streamText(provider, modelId, prompt, parameters);
      
      const encoder = new TextEncoder();
      const stream = new TransformStream();
      const writer = stream.writable.getWriter();

      // Start streaming in the background
      (async () => {
        try {
          for await (const chunk of streamResult.textStream) {
            await writer.write(
              encoder.encode(`data: ${JSON.stringify({ type: 'text', content: chunk })}\n\n`)
            );
          }

          // Send completion message
          await writer.write(
            encoder.encode(`data: ${JSON.stringify({ 
              type: 'complete', 
              usage: streamResult.usage 
            })}\n\n`)
          );
        } catch (error) {
          console.error('[AI API] Streaming error:', error);
          await writer.write(
            encoder.encode(`data: ${JSON.stringify({ 
              type: 'error', 
              error: error instanceof Error ? error.message : 'Streaming failed' 
            })}\n\n`)
          );
        } finally {
          await writer.close();
        }
      })();

      return new Response(stream.readable, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      });
    } else {
      // Handle non-streaming response
      const result = await aiService.generateText(provider, modelId, prompt, parameters);
      return NextResponse.json(result);
    }
  } catch (error) {
    console.error('[AI API] Generation error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    );
  }
}
