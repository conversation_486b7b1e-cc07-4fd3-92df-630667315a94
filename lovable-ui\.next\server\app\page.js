/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNEb2N1bWVudHMlNUMlNUNnaXRodWIlNUMlNUNsb3ZhYmxlLWNsb25lJTVDJTVDbG92YWJsZS11aSU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SUFBaUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb3ZhYmxlLXVpLz81YjRhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVXNlclxcXFxEb2N1bWVudHNcXFxcZ2l0aHViXFxcXGxvdmFibGUtY2xvbmVcXFxcbG92YWJsZS11aVxcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Clib%5C%5Ccontexts%5C%5Cai-config-context.tsx%22%2C%22ids%22%3A%5B%22AIConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Clib%5C%5Ccontexts%5C%5Cai-config-context.tsx%22%2C%22ids%22%3A%5B%22AIConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/contexts/ai-config-context.tsx */ \"(ssr)/./lib/contexts/ai-config-context.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNEb2N1bWVudHMlNUMlNUNnaXRodWIlNUMlNUNsb3ZhYmxlLWNsb25lJTVDJTVDbG92YWJsZS11aSU1QyU1Q2xpYiU1QyU1Q2NvbnRleHRzJTVDJTVDYWktY29uZmlnLWNvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQUlDb25maWdQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNVc2VyJTVDJTVDRG9jdW1lbnRzJTVDJTVDZ2l0aHViJTVDJTVDbG92YWJsZS1jbG9uZSU1QyU1Q2xvdmFibGUtdWklNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDVXNlciU1QyU1Q0RvY3VtZW50cyU1QyU1Q2dpdGh1YiU1QyU1Q2xvdmFibGUtY2xvbmUlNUMlNUNsb3ZhYmxlLXVpJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUE4SyIsInNvdXJjZXMiOlsid2VicGFjazovL2xvdmFibGUtdWkvP2U5MDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBSUNvbmZpZ1Byb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcVXNlclxcXFxEb2N1bWVudHNcXFxcZ2l0aHViXFxcXGxvdmFibGUtY2xvbmVcXFxcbG92YWJsZS11aVxcXFxsaWJcXFxcY29udGV4dHNcXFxcYWktY29uZmlnLWNvbnRleHQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Clib%5C%5Ccontexts%5C%5Cai-config-context.tsx%22%2C%22ids%22%3A%5B%22AIConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Navbar */ \"(ssr)/./components/Navbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [prompt, setPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const handleGenerate = ()=>{\n        if (!prompt.trim()) return;\n        // Navigate to generate page with prompt\n        router.push(`/generate?prompt=${encodeURIComponent(prompt)}`);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"jsx-24f01c58ae8ac726\" + \" \" + \"min-h-screen relative overflow-hidden bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundImage: \"url('/gradient.png')\"\n                },\n                className: \"jsx-24f01c58ae8ac726\" + \" \" + \"absolute inset-0 z-0 bg-cover bg-center\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-24f01c58ae8ac726\" + \" \" + \"relative z-10 flex flex-col items-center justify-center min-h-screen px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-24f01c58ae8ac726\" + \" \" + \"max-w-4xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"text-4xl sm:text-4xl md:text-4xl font-bold text-white mb-6\",\n                            children: \"Build something with Lovable-clone\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"text-xl sm:text-xl text-gray-300 mb-12 max-w-2xl mx-auto\",\n                            children: \"BUILT WITH CLAUDE CODE\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"text-xl sm:text-xl text-gray-300 mb-12 max-w-2xl mx-auto\",\n                            children: \"Turn your ideas into production-ready code in minutes. Powered by Claude's advanced AI capabilities.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"relative max-w-2xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-24f01c58ae8ac726\" + \" \" + \"relative flex items-center bg-black rounded-2xl border border-gray-800 shadow-2xl px-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            placeholder: \"Ask Lovable to create a prototype...\",\n                                            value: prompt,\n                                            onChange: (e)=>setPrompt(e.target.value),\n                                            onKeyDown: (e)=>{\n                                                if (e.key === \"Enter\" && !e.shiftKey) {\n                                                    e.preventDefault();\n                                                    handleGenerate();\n                                                }\n                                            },\n                                            rows: 3,\n                                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"flex-1 px-5 py-4 bg-transparent text-white placeholder-gray-500 focus:outline-none text-lg resize-none min-h-[120px] max-h-[300px]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleGenerate,\n                                            disabled: !prompt.trim(),\n                                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"flex-shrink-0 mr-3 p-3 bg-gray-800 hover:bg-gray-700 text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 group\",\n                                            children:  false ? /*#__PURE__*/ 0 : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                className: \"jsx-24f01c58ae8ac726\" + \" \" + \"h-5 w-5 group-hover:scale-110 transition-transform\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M5 10l7-7m0 0l7 7m-7-7v18\",\n                                                    className: \"jsx-24f01c58ae8ac726\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-24f01c58ae8ac726\" + \" \" + \"mt-8 flex flex-wrap justify-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setPrompt(\"Create a modern blog website with markdown support\"),\n                                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"px-4 py-2 text-sm text-gray-400 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gray-700/50 transition-colors border border-gray-700\",\n                                            children: \"Blog website\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setPrompt(\"Build a portfolio website with project showcase\"),\n                                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"px-4 py-2 text-sm text-gray-400 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gray-700/50 transition-colors border border-gray-700\",\n                                            children: \"Portfolio site\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setPrompt(\"Create an e-commerce product catalog with shopping cart\"),\n                                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"px-4 py-2 text-sm text-gray-400 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gray-700/50 transition-colors border border-gray-700\",\n                                            children: \"E-commerce\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setPrompt(\"Build a dashboard with charts and data visualization\"),\n                                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"px-4 py-2 text-sm text-gray-400 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gray-700/50 transition-colors border border-gray-700\",\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"24f01c58ae8ac726\",\n                children: \"@-webkit-keyframes blob{0%{-webkit-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}33%{-webkit-transform:translate(30px,-50px)scale(1.1);transform:translate(30px,-50px)scale(1.1)}66%{-webkit-transform:translate(-20px,20px)scale(.9);transform:translate(-20px,20px)scale(.9)}100%{-webkit-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}}@-moz-keyframes blob{0%{-moz-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}33%{-moz-transform:translate(30px,-50px)scale(1.1);transform:translate(30px,-50px)scale(1.1)}66%{-moz-transform:translate(-20px,20px)scale(.9);transform:translate(-20px,20px)scale(.9)}100%{-moz-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}}@-o-keyframes blob{0%{-o-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}33%{-o-transform:translate(30px,-50px)scale(1.1);transform:translate(30px,-50px)scale(1.1)}66%{-o-transform:translate(-20px,20px)scale(.9);transform:translate(-20px,20px)scale(.9)}100%{-o-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}}@keyframes blob{0%{-webkit-transform:translate(0px,0px)scale(1);-moz-transform:translate(0px,0px)scale(1);-o-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}33%{-webkit-transform:translate(30px,-50px)scale(1.1);-moz-transform:translate(30px,-50px)scale(1.1);-o-transform:translate(30px,-50px)scale(1.1);transform:translate(30px,-50px)scale(1.1)}66%{-webkit-transform:translate(-20px,20px)scale(.9);-moz-transform:translate(-20px,20px)scale(.9);-o-transform:translate(-20px,20px)scale(.9);transform:translate(-20px,20px)scale(.9)}100%{-webkit-transform:translate(0px,0px)scale(1);-moz-transform:translate(0px,0px)scale(1);-o-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}}.animate-blob.jsx-24f01c58ae8ac726{-webkit-animation:blob 7s infinite;-moz-animation:blob 7s infinite;-o-animation:blob 7s infinite;animation:blob 7s infinite}.animation-delay-2000.jsx-24f01c58ae8ac726{-webkit-animation-delay:2s;-moz-animation-delay:2s;-o-animation-delay:2s;animation-delay:2s}.animation-delay-4000.jsx-24f01c58ae8ac726{-webkit-animation-delay:4s;-moz-animation-delay:4s;-o-animation-delay:4s;animation-delay:4s}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Navbar() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"absolute top-0 left-0 right-0 z-20 flex items-center justify-between px-6 py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/\",\n                        className: \"flex items-center gap-2 text-2xl font-semibold text-white hover:opacity-90 transition-opacity\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-block w-6 h-6 rounded-sm bg-gradient-to-br from-orange-400 via-pink-500 to-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 13,\n                                columnNumber: 11\n                            }, this),\n                            \"Lovable\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center gap-8 text-sm text-gray-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"hover:text-white transition-colors\",\n                                children: \"Community\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"hover:text-white transition-colors\",\n                                children: \"Enterprise\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"hover:text-white transition-colors\",\n                                children: \"Learn\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"hover:text-white transition-colors\",\n                                children: \"Shipped\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4 text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/settings\",\n                        className: \"text-gray-300 hover:text-white transition-colors flex items-center gap-1\",\n                        title: \"AI Settings\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this),\n                            \"Settings\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"#\",\n                        className: \"text-gray-300 hover:text-white transition-colors\",\n                        children: \"Log in\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"#\",\n                        className: \"px-4 py-2 bg-white text-black rounded-lg font-semibold hover:bg-gray-100 transition-colors\",\n                        children: \"Get started\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/contexts/ai-config-context.tsx":
/*!********************************************!*\
  !*** ./lib/contexts/ai-config-context.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIConfigProvider: () => (/* binding */ AIConfigProvider),\n/* harmony export */   useAIConfig: () => (/* binding */ useAIConfig)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types_ai_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../types/ai-config */ \"(ssr)/./lib/types/ai-config.ts\");\n/* __next_internal_client_entry_do_not_use__ AIConfigProvider,useAIConfig auto */ \n\n\n// Reducer\nfunction aiConfigReducer(state, action) {\n    switch(action.type){\n        case \"SET_CONFIG\":\n            return {\n                ...state,\n                config: action.payload,\n                error: null\n            };\n        case \"ADD_PROVIDER\":\n            return {\n                ...state,\n                config: {\n                    ...state.config,\n                    providers: [\n                        ...state.config.providers,\n                        action.payload\n                    ]\n                }\n            };\n        case \"UPDATE_PROVIDER\":\n            return {\n                ...state,\n                config: {\n                    ...state.config,\n                    providers: state.config.providers.map((provider)=>provider.id === action.payload.id ? {\n                            ...provider,\n                            ...action.payload.config\n                        } : provider)\n                }\n            };\n        case \"REMOVE_PROVIDER\":\n            const updatedProviders = state.config.providers.filter((p)=>p.id !== action.payload);\n            return {\n                ...state,\n                config: {\n                    ...state.config,\n                    providers: updatedProviders,\n                    // Reset default provider if it was removed\n                    defaultProvider: state.config.defaultProvider === action.payload ? updatedProviders[0]?.id : state.config.defaultProvider\n                }\n            };\n        case \"SET_DEFAULT_PROVIDER\":\n            return {\n                ...state,\n                config: {\n                    ...state.config,\n                    defaultProvider: action.payload\n                }\n            };\n        case \"SET_DEFAULT_MODEL\":\n            return {\n                ...state,\n                config: {\n                    ...state.config,\n                    defaultModel: action.payload\n                }\n            };\n        case \"UPDATE_DEFAULT_PARAMETERS\":\n            return {\n                ...state,\n                config: {\n                    ...state.config,\n                    defaultParameters: {\n                        ...state.config.defaultParameters,\n                        ...action.payload\n                    }\n                }\n            };\n        case \"TOGGLE_PROVIDER\":\n            return {\n                ...state,\n                config: {\n                    ...state.config,\n                    providers: state.config.providers.map((provider)=>provider.id === action.payload ? {\n                            ...provider,\n                            enabled: !provider.enabled\n                        } : provider)\n                }\n            };\n        case \"RESET_CONFIG\":\n            return {\n                ...state,\n                config: _types_ai_config__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_AI_CONFIG,\n                error: null\n            };\n        default:\n            return state;\n    }\n}\n// Initial State\nconst initialState = {\n    config: _types_ai_config__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_AI_CONFIG,\n    isLoading: false,\n    error: null\n};\n// Context\nconst AIConfigContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Storage Key\nconst STORAGE_KEY = \"ai-config\";\nfunction AIConfigProvider({ children }) {\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(aiConfigReducer, initialState);\n    // Load config on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadConfig();\n    }, []);\n    // Save config whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (state.config !== _types_ai_config__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_AI_CONFIG) {\n            saveConfig();\n        }\n    }, [\n        state.config\n    ]);\n    const saveConfig = async ()=>{\n        try {\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(state.config));\n        } catch (error) {\n            console.error(\"Failed to save AI config:\", error);\n        }\n    };\n    const loadConfig = async ()=>{\n        try {\n            const saved = localStorage.getItem(STORAGE_KEY);\n            if (saved) {\n                const config = JSON.parse(saved);\n                dispatch({\n                    type: \"SET_CONFIG\",\n                    payload: config\n                });\n            }\n        } catch (error) {\n            console.error(\"Failed to load AI config:\", error);\n        }\n    };\n    const testConnection = async (providerId)=>{\n        const provider = state.config.providers.find((p)=>p.id === providerId);\n        if (!provider) {\n            return {\n                success: false,\n                error: \"Provider not found\"\n            };\n        }\n        try {\n            const response = await fetch(\"/api/ai/test-connection\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    provider\n                })\n            });\n            return await response.json();\n        } catch (error) {\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : \"Connection test failed\"\n            };\n        }\n    };\n    const validateProvider = (provider)=>{\n        const errors = [];\n        if (!provider.name.trim()) {\n            errors.push(\"Provider name is required\");\n        }\n        if (!provider.apiKey.trim()) {\n            errors.push(\"API key is required\");\n        }\n        if (provider.provider === \"custom\") {\n            const customProvider = provider;\n            if (!customProvider.baseURL?.trim()) {\n                errors.push(\"Base URL is required for custom providers\");\n            }\n            if (!customProvider.modelName?.trim()) {\n                errors.push(\"Model name is required for custom providers\");\n            }\n        }\n        return {\n            isValid: errors.length === 0,\n            errors\n        };\n    };\n    const actions = {\n        setConfig: (config)=>dispatch({\n                type: \"SET_CONFIG\",\n                payload: config\n            }),\n        addProvider: (provider)=>dispatch({\n                type: \"ADD_PROVIDER\",\n                payload: provider\n            }),\n        updateProvider: (id, updates)=>dispatch({\n                type: \"UPDATE_PROVIDER\",\n                payload: {\n                    id,\n                    config: updates\n                }\n            }),\n        removeProvider: (id)=>dispatch({\n                type: \"REMOVE_PROVIDER\",\n                payload: id\n            }),\n        setDefaultProvider: (providerId)=>dispatch({\n                type: \"SET_DEFAULT_PROVIDER\",\n                payload: providerId\n            }),\n        setDefaultModel: (modelId)=>dispatch({\n                type: \"SET_DEFAULT_MODEL\",\n                payload: modelId\n            }),\n        updateDefaultParameters: (parameters)=>dispatch({\n                type: \"UPDATE_DEFAULT_PARAMETERS\",\n                payload: parameters\n            }),\n        toggleProvider: (id)=>dispatch({\n                type: \"TOGGLE_PROVIDER\",\n                payload: id\n            }),\n        resetConfig: ()=>dispatch({\n                type: \"RESET_CONFIG\"\n            }),\n        saveConfig,\n        loadConfig,\n        testConnection,\n        validateProvider\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIConfigContext.Provider, {\n        value: {\n            state,\n            actions\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\lib\\\\contexts\\\\ai-config-context.tsx\",\n        lineNumber: 274,\n        columnNumber: 5\n    }, this);\n}\n// Hook\nfunction useAIConfig() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AIConfigContext);\n    if (context === undefined) {\n        throw new Error(\"useAIConfig must be used within an AIConfigProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/contexts/ai-config-context.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/types/ai-config.ts":
/*!********************************!*\
  !*** ./lib/types/ai-config.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AVAILABLE_MODELS: () => (/* binding */ AVAILABLE_MODELS),\n/* harmony export */   DEFAULT_AI_CONFIG: () => (/* binding */ DEFAULT_AI_CONFIG)\n/* harmony export */ });\n// AI Provider Types\n// Available Models by Provider\nconst AVAILABLE_MODELS = {\n    openai: [\n        {\n            id: \"gpt-4o\",\n            name: \"GPT-4o\",\n            provider: \"openai\",\n            maxTokens: 4096,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 128000\n        },\n        {\n            id: \"gpt-4o-mini\",\n            name: \"GPT-4o Mini\",\n            provider: \"openai\",\n            maxTokens: 16384,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 128000\n        },\n        {\n            id: \"gpt-4-turbo\",\n            name: \"GPT-4 Turbo\",\n            provider: \"openai\",\n            maxTokens: 4096,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 128000\n        },\n        {\n            id: \"gpt-3.5-turbo\",\n            name: \"GPT-3.5 Turbo\",\n            provider: \"openai\",\n            maxTokens: 4096,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: false,\n            contextWindow: 16385\n        }\n    ],\n    anthropic: [\n        {\n            id: \"claude-3-5-sonnet-20241022\",\n            name: \"Claude 3.5 Sonnet\",\n            provider: \"anthropic\",\n            maxTokens: 8192,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 200000\n        },\n        {\n            id: \"claude-3-5-haiku-20241022\",\n            name: \"Claude 3.5 Haiku\",\n            provider: \"anthropic\",\n            maxTokens: 8192,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 200000\n        },\n        {\n            id: \"claude-3-opus-20240229\",\n            name: \"Claude 3 Opus\",\n            provider: \"anthropic\",\n            maxTokens: 4096,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 200000\n        }\n    ],\n    google: [\n        {\n            id: \"gemini-1.5-pro\",\n            name: \"Gemini 1.5 Pro\",\n            provider: \"google\",\n            maxTokens: 8192,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 2000000\n        },\n        {\n            id: \"gemini-1.5-flash\",\n            name: \"Gemini 1.5 Flash\",\n            provider: \"google\",\n            maxTokens: 8192,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 1000000\n        }\n    ],\n    cohere: [\n        {\n            id: \"command-r-plus\",\n            name: \"Command R+\",\n            provider: \"cohere\",\n            maxTokens: 4096,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: false,\n            contextWindow: 128000\n        },\n        {\n            id: \"command-r\",\n            name: \"Command R\",\n            provider: \"cohere\",\n            maxTokens: 4096,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: false,\n            contextWindow: 128000\n        }\n    ],\n    mistral: [\n        {\n            id: \"mistral-large-latest\",\n            name: \"Mistral Large\",\n            provider: \"mistral\",\n            maxTokens: 8192,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: false,\n            contextWindow: 128000\n        },\n        {\n            id: \"mistral-medium-latest\",\n            name: \"Mistral Medium\",\n            provider: \"mistral\",\n            maxTokens: 8192,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: false,\n            contextWindow: 32000\n        }\n    ],\n    custom: []\n};\n// Default Configuration\nconst DEFAULT_AI_CONFIG = {\n    providers: [],\n    defaultParameters: {\n        temperature: 0.7,\n        maxTokens: 4096,\n        topP: 1,\n        frequencyPenalty: 0,\n        presencePenalty: 0\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/types/ai-config.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ea662773702b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb3ZhYmxlLXVpLy4vYXBwL2dsb2JhbHMuY3NzPzdjNTQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlYTY2Mjc3MzcwMmJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _lib_contexts_ai_config_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/contexts/ai-config-context */ \"(rsc)/./lib/contexts/ai-config-context.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"Lovable Clone - AI-Powered Code Generation\",\n    description: \"Build applications faster with AI-powered code generation\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_contexts_ai_config_context__WEBPACK_IMPORTED_MODULE_2__.AIConfigProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFLTUE7QUFIaUI7QUFDNkM7QUFJN0QsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1YsMkpBQWU7c0JBQzlCLDRFQUFDQyw2RUFBZ0JBOzBCQUNkSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG92YWJsZS11aS8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XHJcbmltcG9ydCB7IEludGVyIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcclxuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xyXG5pbXBvcnQgeyBBSUNvbmZpZ1Byb3ZpZGVyIH0gZnJvbSBcIkAvbGliL2NvbnRleHRzL2FpLWNvbmZpZy1jb250ZXh0XCI7XHJcblxyXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogW1wibGF0aW5cIl0gfSk7XHJcblxyXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xyXG4gIHRpdGxlOiBcIkxvdmFibGUgQ2xvbmUgLSBBSS1Qb3dlcmVkIENvZGUgR2VuZXJhdGlvblwiLFxyXG4gIGRlc2NyaXB0aW9uOiBcIkJ1aWxkIGFwcGxpY2F0aW9ucyBmYXN0ZXIgd2l0aCBBSS1wb3dlcmVkIGNvZGUgZ2VuZXJhdGlvblwiLFxyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IFJlYWRvbmx5PHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xyXG59Pikge1xyXG4gIHJldHVybiAoXHJcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cclxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxyXG4gICAgICAgIDxBSUNvbmZpZ1Byb3ZpZGVyPlxyXG4gICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgIDwvQUlDb25maWdQcm92aWRlcj5cclxuICAgICAgPC9ib2R5PlxyXG4gICAgPC9odG1sPlxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsiaW50ZXIiLCJBSUNvbmZpZ1Byb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\app\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./lib/contexts/ai-config-context.tsx":
/*!********************************************!*\
  !*** ./lib/contexts/ai-config-context.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AIConfigProvider: () => (/* binding */ e0),
/* harmony export */   useAIConfig: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\contexts\ai-config-context.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\contexts\ai-config-context.tsx#AIConfigProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\contexts\ai-config-context.tsx#useAIConfig`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@swc","vendor-chunks/styled-jsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();