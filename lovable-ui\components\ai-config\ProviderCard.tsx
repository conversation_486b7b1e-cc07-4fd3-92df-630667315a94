"use client";

import { useState } from 'react';
import { ProviderConfig, TestConnectionResult } from '@/lib/types/ai-config';

interface ProviderCardProps {
  provider: ProviderConfig;
  isDefault: boolean;
  onEdit: () => void;
  onDelete: () => void;
  onTest: () => Promise<TestConnectionResult>;
  onToggle: () => void;
  onSetDefault: () => void;
}

export default function ProviderCard({
  provider,
  isDefault,
  onEdit,
  onDelete,
  onTest,
  onToggle,
  onSetDefault,
}: ProviderCardProps) {
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<TestConnectionResult | null>(null);

  const handleTest = async () => {
    setTesting(true);
    setTestResult(null);
    try {
      const result = await onTest();
      setTestResult(result);
    } catch (error) {
      setTestResult({
        success: false,
        error: error instanceof Error ? error.message : 'Test failed',
      });
    } finally {
      setTesting(false);
    }
  };

  const getProviderIcon = (providerType: string) => {
    switch (providerType) {
      case 'openai':
        return '🤖';
      case 'anthropic':
        return '🧠';
      case 'google':
        return '🔍';
      case 'cohere':
        return '💬';
      case 'mistral':
        return '🌪️';
      case 'custom':
        return '⚙️';
      default:
        return '🤖';
    }
  };

  const getProviderDisplayName = (providerType: string) => {
    switch (providerType) {
      case 'openai':
        return 'OpenAI';
      case 'anthropic':
        return 'Anthropic';
      case 'google':
        return 'Google';
      case 'cohere':
        return 'Cohere';
      case 'mistral':
        return 'Mistral';
      case 'custom':
        return 'Custom';
      default:
        return providerType;
    }
  };

  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700 p-4">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="text-2xl">{getProviderIcon(provider.provider)}</div>
          <div>
            <div className="flex items-center space-x-2">
              <h3 className="text-lg font-semibold text-white">{provider.name}</h3>
              {isDefault && (
                <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
                  Default
                </span>
              )}
              {!provider.enabled && (
                <span className="bg-gray-600 text-white text-xs px-2 py-1 rounded-full">
                  Disabled
                </span>
              )}
            </div>
            <p className="text-gray-400 text-sm">
              {getProviderDisplayName(provider.provider)}
              {provider.provider === 'custom' && provider.baseURL && (
                <span className="ml-1">• {new URL(provider.baseURL).hostname}</span>
              )}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={onToggle}
            className={`w-12 h-6 rounded-full transition-colors ${
              provider.enabled ? 'bg-green-600' : 'bg-gray-600'
            }`}
          >
            <div
              className={`w-5 h-5 bg-white rounded-full transition-transform ${
                provider.enabled ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
      </div>

      {/* API Key Status */}
      <div className="mb-4">
        <div className="flex items-center space-x-2 text-sm">
          <span className="text-gray-400">API Key:</span>
          <span className="text-green-400">
            {provider.apiKey ? '••••••••' : 'Not set'}
          </span>
        </div>
      </div>

      {/* Test Result */}
      {testResult && (
        <div className={`mb-4 p-3 rounded-lg text-sm ${
          testResult.success 
            ? 'bg-green-900 border border-green-700 text-green-100'
            : 'bg-red-900 border border-red-700 text-red-100'
        }`}>
          {testResult.success ? (
            <div>
              <div className="font-medium">✓ Connection successful</div>
              {testResult.latency && (
                <div className="text-xs opacity-75 mt-1">
                  Response time: {testResult.latency}ms
                </div>
              )}
            </div>
          ) : (
            <div>
              <div className="font-medium">✗ Connection failed</div>
              {testResult.error && (
                <div className="text-xs opacity-75 mt-1">{testResult.error}</div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <button
            onClick={handleTest}
            disabled={testing || !provider.enabled}
            className="bg-gray-700 hover:bg-gray-600 disabled:bg-gray-800 disabled:text-gray-500 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
          >
            {testing ? 'Testing...' : 'Test'}
          </button>
          
          {!isDefault && provider.enabled && (
            <button
              onClick={onSetDefault}
              className="bg-blue-700 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
            >
              Set Default
            </button>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={onEdit}
            className="text-gray-400 hover:text-white transition-colors"
            title="Edit provider"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </button>
          
          <button
            onClick={onDelete}
            className="text-gray-400 hover:text-red-400 transition-colors"
            title="Delete provider"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}
