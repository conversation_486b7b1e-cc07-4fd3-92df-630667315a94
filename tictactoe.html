<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tic-<PERSON><PERSON>-<PERSON>e</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .container {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            text-align: center;
        }

        h1 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 2.5rem;
        }

        .status {
            font-size: 1.25rem;
            color: #666;
            margin-bottom: 1.5rem;
            min-height: 30px;
        }

        .board {
            display: grid;
            grid-template-columns: repeat(3, 120px);
            grid-template-rows: repeat(3, 120px);
            gap: 10px;
            margin: 0 auto 2rem;
            background: #333;
            padding: 10px;
            border-radius: 10px;
        }

        .cell {
            background: white;
            border: none;
            font-size: 2.5rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #333;
        }

        .cell:hover:not(.taken) {
            background: #f0f0f0;
            transform: scale(0.95);
        }

        .cell.taken {
            cursor: not-allowed;
        }

        .cell.x {
            color: #667eea;
        }

        .cell.o {
            color: #764ba2;
        }

        .cell.winner {
            background: #ffe66d;
            animation: pulse 0.5s ease-in-out infinite alternate;
        }

        @keyframes pulse {
            from { transform: scale(1); }
            to { transform: scale(1.05); }
        }

        .reset-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            font-size: 1.1rem;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .reset-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .reset-btn:active {
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Tic-Tac-Toe</h1>
        <div class="status" id="status">Player X's turn</div>
        <div class="board" id="board">
            <button class="cell" data-index="0"></button>
            <button class="cell" data-index="1"></button>
            <button class="cell" data-index="2"></button>
            <button class="cell" data-index="3"></button>
            <button class="cell" data-index="4"></button>
            <button class="cell" data-index="5"></button>
            <button class="cell" data-index="6"></button>
            <button class="cell" data-index="7"></button>
            <button class="cell" data-index="8"></button>
        </div>
        <button class="reset-btn" id="resetBtn">New Game</button>
    </div>

    <script>
        const cells = document.querySelectorAll('.cell');
        const statusDisplay = document.getElementById('status');
        const resetBtn = document.getElementById('resetBtn');
        
        let currentPlayer = 'X';
        let gameActive = true;
        let gameState = ['', '', '', '', '', '', '', '', ''];
        
        const winningConditions = [
            [0, 1, 2],
            [3, 4, 5],
            [6, 7, 8],
            [0, 3, 6],
            [1, 4, 7],
            [2, 5, 8],
            [0, 4, 8],
            [2, 4, 6]
        ];

        function handleCellClick(e) {
            const cell = e.target;
            const index = parseInt(cell.getAttribute('data-index'));
            
            if (gameState[index] !== '' || !gameActive) {
                return;
            }
            
            gameState[index] = currentPlayer;
            cell.textContent = currentPlayer;
            cell.classList.add('taken', currentPlayer.toLowerCase());
            
            checkResult();
        }

        function checkResult() {
            let roundWon = false;
            let winningCombination = [];
            
            for (let i = 0; i < winningConditions.length; i++) {
                const [a, b, c] = winningConditions[i];
                if (gameState[a] === '' || gameState[b] === '' || gameState[c] === '') {
                    continue;
                }
                if (gameState[a] === gameState[b] && gameState[b] === gameState[c]) {
                    roundWon = true;
                    winningCombination = [a, b, c];
                    break;
                }
            }
            
            if (roundWon) {
                statusDisplay.textContent = `Player ${currentPlayer} wins!`;
                gameActive = false;
                highlightWinningCells(winningCombination);
                return;
            }
            
            if (!gameState.includes('')) {
                statusDisplay.textContent = "It's a draw!";
                gameActive = false;
                return;
            }
            
            currentPlayer = currentPlayer === 'X' ? 'O' : 'X';
            statusDisplay.textContent = `Player ${currentPlayer}'s turn`;
        }

        function highlightWinningCells(combination) {
            combination.forEach(index => {
                cells[index].classList.add('winner');
            });
        }

        function resetGame() {
            currentPlayer = 'X';
            gameActive = true;
            gameState = ['', '', '', '', '', '', '', '', ''];
            statusDisplay.textContent = `Player ${currentPlayer}'s turn`;
            
            cells.forEach(cell => {
                cell.textContent = '';
                cell.classList.remove('taken', 'x', 'o', 'winner');
            });
        }

        cells.forEach(cell => cell.addEventListener('click', handleCellClick));
        resetBtn.addEventListener('click', resetGame);
    </script>
</body>
</html>