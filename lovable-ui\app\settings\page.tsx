"use client";

import { useState } from 'react';
import Navbar from '@/components/Navbar';
import { useAIConfig } from '@/lib/contexts/ai-config-context';
import { ProviderConfig, AIProvider, AVAILABLE_MODELS } from '@/lib/types/ai-config';
import ProviderCard from '@/components/ai-config/ProviderCard';
import AddProviderModal from '@/components/ai-config/AddProviderModal';
import DefaultSettingsCard from '@/components/ai-config/DefaultSettingsCard';

export default function SettingsPage() {
  const { state, actions } = useAIConfig();
  const [showAddProvider, setShowAddProvider] = useState(false);
  const [editingProvider, setEditingProvider] = useState<ProviderConfig | null>(null);

  const handleAddProvider = (provider: ProviderConfig) => {
    actions.addProvider(provider);
    setShowAddProvider(false);
  };

  const handleEditProvider = (provider: ProviderConfig) => {
    setEditingProvider(provider);
    setShowAddProvider(true);
  };

  const handleUpdateProvider = (provider: ProviderConfig) => {
    if (editingProvider) {
      actions.updateProvider(editingProvider.id, provider);
      setEditingProvider(null);
      setShowAddProvider(false);
    }
  };

  const handleDeleteProvider = (id: string) => {
    if (confirm('Are you sure you want to delete this provider?')) {
      actions.removeProvider(id);
    }
  };

  const handleTestConnection = async (id: string) => {
    return await actions.testConnection(id);
  };

  const handleToggleProvider = (id: string) => {
    actions.toggleProvider(id);
  };

  const handleSetDefault = (id: string) => {
    actions.setDefaultProvider(id);
  };

  return (
    <main className="min-h-screen bg-black">
      <Navbar />
      <div className="h-16" /> {/* Spacer for navbar */}
      
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">AI Configuration</h1>
          <p className="text-gray-400">
            Configure AI providers, models, and default settings for code generation.
          </p>
        </div>

        {/* Default Settings */}
        <DefaultSettingsCard />

        {/* Providers Section */}
        <div className="bg-gray-900 rounded-lg border border-gray-800 p-6 mb-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-xl font-semibold text-white mb-1">AI Providers</h2>
              <p className="text-gray-400 text-sm">
                Add and configure AI providers for code generation.
              </p>
            </div>
            <button
              onClick={() => setShowAddProvider(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              Add Provider
            </button>
          </div>

          {state.config.providers.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-white mb-2">No providers configured</h3>
              <p className="text-gray-400 mb-4">
                Add your first AI provider to start generating code.
              </p>
              <button
                onClick={() => setShowAddProvider(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
              >
                Add Provider
              </button>
            </div>
          ) : (
            <div className="grid gap-4">
              {state.config.providers.map((provider) => (
                <ProviderCard
                  key={provider.id}
                  provider={provider}
                  isDefault={state.config.defaultProvider === provider.id}
                  onEdit={() => handleEditProvider(provider)}
                  onDelete={() => handleDeleteProvider(provider.id)}
                  onTest={() => handleTestConnection(provider.id)}
                  onToggle={() => handleToggleProvider(provider.id)}
                  onSetDefault={() => handleSetDefault(provider.id)}
                />
              ))}
            </div>
          )}
        </div>

        {/* Configuration Info */}
        <div className="bg-gray-900 rounded-lg border border-gray-800 p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Configuration Status</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gray-800 rounded-lg p-4">
              <div className="text-2xl font-bold text-white mb-1">
                {state.config.providers.length}
              </div>
              <div className="text-gray-400 text-sm">Total Providers</div>
            </div>
            <div className="bg-gray-800 rounded-lg p-4">
              <div className="text-2xl font-bold text-white mb-1">
                {state.config.providers.filter(p => p.enabled).length}
              </div>
              <div className="text-gray-400 text-sm">Active Providers</div>
            </div>
            <div className="bg-gray-800 rounded-lg p-4">
              <div className="text-2xl font-bold text-white mb-1">
                {state.config.defaultProvider ? '✓' : '✗'}
              </div>
              <div className="text-gray-400 text-sm">Default Set</div>
            </div>
          </div>
        </div>
      </div>

      {/* Add/Edit Provider Modal */}
      {showAddProvider && (
        <AddProviderModal
          provider={editingProvider}
          onSave={editingProvider ? handleUpdateProvider : handleAddProvider}
          onCancel={() => {
            setShowAddProvider(false);
            setEditingProvider(null);
          }}
        />
      )}
    </main>
  );
}
