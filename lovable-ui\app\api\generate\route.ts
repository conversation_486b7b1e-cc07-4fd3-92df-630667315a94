import { NextRequest } from "next/server";
import { AIConfigService } from "@/lib/services/ai-config-service";
import { AIProviderService } from "@/lib/services/ai-provider-service";
import { OpencoderService } from "@/lib/services/opencoder-service";

export async function POST(req: NextRequest) {
  try {
    const { prompt, useNewSystem, useOpencoder = true } = await req.json();

    if (!prompt) {
      return new Response(
        JSON.stringify({ error: "Prompt is required" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    console.log("[API] Starting code generation for prompt:", prompt);

    // Check if we should use the new AI configuration system with opencoder
    const configService = AIConfigService.getInstance();
    const shouldUseNewSystem = (useNewSystem || useOpencoder) && configService.hasEnabledProviders();

    if (shouldUseNewSystem) {
      return await handleOpencoderGeneration(prompt, configService);
    }

    // Fallback to legacy system (though Claude Code is removed)
    return new Response(
      JSON.stringify({ error: "Legacy Claude Code system is no longer available. Please configure an AI provider in settings." }),
      { status: 400, headers: { "Content-Type": "application/json" } }
    );
    
  } catch (error: any) {
    console.error("[API] Error:", error);
    return new Response(
      JSON.stringify({ error: error.message || "Internal server error" }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
}

async function handleOpencoderGeneration(prompt: string, configService: AIConfigService) {
  try {
    const defaultProvider = configService.getDefaultProvider();
    const defaultModel = configService.getDefaultModel();

    if (!defaultProvider || !defaultModel) {
      return new Response(
        JSON.stringify({ error: "No default AI provider or model configured" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    console.log(`[API] Using opencoder with ${defaultProvider.name} - ${defaultModel}`);

    const opencoderService = OpencoderService.getInstance();
    const result = await opencoderService.generateCode({
      prompt,
      provider: defaultProvider,
      modelId: defaultModel,
      parameters: configService.getConfig().defaultParameters,
      systemPrompt: "You are an expert software developer. Generate high-quality, production-ready code based on the user's requirements. Focus on creating complete, functional implementations with proper error handling, documentation, and best practices.",
    });

    if (!result.success) {
      return new Response(
        JSON.stringify({ error: result.error || "Code generation failed" }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }

    // Create a streaming response to match the expected format
    const encoder = new TextEncoder();
    const stream = new TransformStream();
    const writer = stream.writable.getWriter();

    // Start streaming in the background
    (async () => {
      try {
        // Send each message from opencoder
        if (result.messages) {
          for (const message of result.messages) {
            await writer.write(
              encoder.encode(`data: ${JSON.stringify(message)}\n\n`)
            );

            // Add a small delay to simulate real-time streaming
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        }

        // Send completion signal
        await writer.write(encoder.encode("data: [DONE]\n\n"));
      } catch (error: any) {
        console.error("[API] Opencoder streaming error:", error);
        await writer.write(
          encoder.encode(`data: ${JSON.stringify({ error: error.message })}\n\n`)
        );
      } finally {
        await writer.close();
      }
    })();

    return new Response(stream.readable, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
      },
    });
  } catch (error: any) {
    console.error("[API] Opencoder error:", error);
    return new Response(
      JSON.stringify({ error: error.message || "Opencoder generation failed" }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
}