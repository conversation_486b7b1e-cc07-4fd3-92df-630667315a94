"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/settings/page",{

/***/ "(app-pages-browser)/./lib/types/ai-config.ts":
/*!********************************!*\
  !*** ./lib/types/ai-config.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AVAILABLE_MODELS: function() { return /* binding */ AVAILABLE_MODELS; },\n/* harmony export */   DEFAULT_AI_CONFIG: function() { return /* binding */ DEFAULT_AI_CONFIG; }\n/* harmony export */ });\n// AI Provider Types\n// Available Models by Provider\nconst AVAILABLE_MODELS = {\n    openai: [\n        {\n            id: \"gpt-4o\",\n            name: \"GPT-4o\",\n            provider: \"openai\",\n            maxTokens: 4096,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 128000\n        },\n        {\n            id: \"gpt-4o-mini\",\n            name: \"GPT-4o Mini\",\n            provider: \"openai\",\n            maxTokens: 16384,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 128000\n        },\n        {\n            id: \"gpt-4-turbo\",\n            name: \"GPT-4 Turbo\",\n            provider: \"openai\",\n            maxTokens: 4096,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 128000\n        },\n        {\n            id: \"gpt-3.5-turbo\",\n            name: \"GPT-3.5 Turbo\",\n            provider: \"openai\",\n            maxTokens: 4096,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: false,\n            contextWindow: 16385\n        }\n    ],\n    anthropic: [\n        {\n            id: \"claude-3-5-sonnet-20241022\",\n            name: \"Claude 3.5 Sonnet\",\n            provider: \"anthropic\",\n            maxTokens: 8192,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 200000\n        },\n        {\n            id: \"claude-3-5-haiku-20241022\",\n            name: \"Claude 3.5 Haiku\",\n            provider: \"anthropic\",\n            maxTokens: 8192,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 200000\n        },\n        {\n            id: \"claude-3-opus-20240229\",\n            name: \"Claude 3 Opus\",\n            provider: \"anthropic\",\n            maxTokens: 4096,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 200000\n        }\n    ],\n    google: [\n        {\n            id: \"gemini-1.5-pro\",\n            name: \"Gemini 1.5 Pro\",\n            provider: \"google\",\n            maxTokens: 8192,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 2000000\n        },\n        {\n            id: \"gemini-1.5-flash\",\n            name: \"Gemini 1.5 Flash\",\n            provider: \"google\",\n            maxTokens: 8192,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 1000000\n        }\n    ],\n    cohere: [\n        {\n            id: \"command-r-plus\",\n            name: \"Command R+\",\n            provider: \"cohere\",\n            maxTokens: 4096,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: false,\n            contextWindow: 128000\n        },\n        {\n            id: \"command-r\",\n            name: \"Command R\",\n            provider: \"cohere\",\n            maxTokens: 4096,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: false,\n            contextWindow: 128000\n        }\n    ],\n    mistral: [\n        {\n            id: \"mistral-large-latest\",\n            name: \"Mistral Large\",\n            provider: \"mistral\",\n            maxTokens: 8192,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: false,\n            contextWindow: 128000\n        },\n        {\n            id: \"mistral-medium-latest\",\n            name: \"Mistral Medium\",\n            provider: \"mistral\",\n            maxTokens: 8192,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: false,\n            contextWindow: 32000\n        }\n    ],\n    custom: []\n};\n// Default Configuration\nconst DEFAULT_AI_CONFIG = {\n    providers: [],\n    defaultParameters: {\n        temperature: 0.7,\n        maxTokens: 4096,\n        topP: 1,\n        frequencyPenalty: 0,\n        presencePenalty: 0\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/types/ai-config.ts\n"));

/***/ })

});