// AI Provider Types
export type AIProvider =
  | 'openai'
  | 'anthropic'
  | 'google'
  | 'cohere'
  | 'mistral'
  | 'daytona'
  | 'custom';

// Model Configuration Types
export interface ModelConfig {
  id: string;
  name: string;
  provider: AIProvider;
  maxTokens?: number;
  supportsStreaming?: boolean;
  supportsTools?: boolean;
  supportsVision?: boolean;
  contextWindow?: number;
}

// Provider Configuration Types
export interface BaseProviderConfig {
  id: string;
  name: string;
  provider: AIProvider;
  apiKey: string;
  enabled: boolean;
  isDefault?: boolean;
}

export interface OpenAIConfig extends BaseProviderConfig {
  provider: 'openai';
  baseURL?: string;
  organization?: string;
  project?: string;
}

export interface AnthropicConfig extends BaseProviderConfig {
  provider: 'anthropic';
  baseURL?: string;
}

export interface GoogleConfig extends BaseProviderConfig {
  provider: 'google';
  baseURL?: string;
}

export interface CohereConfig extends BaseProviderConfig {
  provider: 'cohere';
  baseURL?: string;
}

export interface MistralConfig extends BaseProviderConfig {
  provider: 'mistral';
  baseURL?: string;
}

export interface CustomConfig extends BaseProviderConfig {
  provider: 'custom';
  baseURL: string;
  modelName: string;
  headers?: Record<string, string>;
}

export interface DaytonaConfig extends BaseProviderConfig {
  provider: 'daytona';
  apiEndpoint?: string;
  defaultImage?: string;
  defaultProjectPath?: string;
}

export type ProviderConfig =
  | OpenAIConfig
  | AnthropicConfig
  | GoogleConfig
  | CohereConfig
  | MistralConfig
  | DaytonaConfig
  | CustomConfig;

// Model Parameters
export interface ModelParameters {
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  topK?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stop?: string[];
}

// AI Configuration
export interface AIConfiguration {
  providers: ProviderConfig[];
  defaultProvider?: string;
  defaultModel?: string;
  defaultParameters: ModelParameters;
}

// Available Models by Provider
export const AVAILABLE_MODELS: Record<AIProvider, ModelConfig[]> = {
  openai: [
    {
      id: 'gpt-4o',
      name: 'GPT-4o',
      provider: 'openai',
      maxTokens: 4096,
      supportsStreaming: true,
      supportsTools: true,
      supportsVision: true,
      contextWindow: 128000,
    },
    {
      id: 'gpt-4o-mini',
      name: 'GPT-4o Mini',
      provider: 'openai',
      maxTokens: 16384,
      supportsStreaming: true,
      supportsTools: true,
      supportsVision: true,
      contextWindow: 128000,
    },
    {
      id: 'gpt-4-turbo',
      name: 'GPT-4 Turbo',
      provider: 'openai',
      maxTokens: 4096,
      supportsStreaming: true,
      supportsTools: true,
      supportsVision: true,
      contextWindow: 128000,
    },
    {
      id: 'gpt-3.5-turbo',
      name: 'GPT-3.5 Turbo',
      provider: 'openai',
      maxTokens: 4096,
      supportsStreaming: true,
      supportsTools: true,
      supportsVision: false,
      contextWindow: 16385,
    },
  ],
  anthropic: [
    {
      id: 'claude-3-5-sonnet-20241022',
      name: 'Claude 3.5 Sonnet',
      provider: 'anthropic',
      maxTokens: 8192,
      supportsStreaming: true,
      supportsTools: true,
      supportsVision: true,
      contextWindow: 200000,
    },
    {
      id: 'claude-3-5-haiku-20241022',
      name: 'Claude 3.5 Haiku',
      provider: 'anthropic',
      maxTokens: 8192,
      supportsStreaming: true,
      supportsTools: true,
      supportsVision: true,
      contextWindow: 200000,
    },
    {
      id: 'claude-3-opus-20240229',
      name: 'Claude 3 Opus',
      provider: 'anthropic',
      maxTokens: 4096,
      supportsStreaming: true,
      supportsTools: true,
      supportsVision: true,
      contextWindow: 200000,
    },
  ],
  google: [
    {
      id: 'gemini-1.5-pro',
      name: 'Gemini 1.5 Pro',
      provider: 'google',
      maxTokens: 8192,
      supportsStreaming: true,
      supportsTools: true,
      supportsVision: true,
      contextWindow: 2000000,
    },
    {
      id: 'gemini-1.5-flash',
      name: 'Gemini 1.5 Flash',
      provider: 'google',
      maxTokens: 8192,
      supportsStreaming: true,
      supportsTools: true,
      supportsVision: true,
      contextWindow: 1000000,
    },
  ],
  cohere: [
    {
      id: 'command-r-plus',
      name: 'Command R+',
      provider: 'cohere',
      maxTokens: 4096,
      supportsStreaming: true,
      supportsTools: true,
      supportsVision: false,
      contextWindow: 128000,
    },
    {
      id: 'command-r',
      name: 'Command R',
      provider: 'cohere',
      maxTokens: 4096,
      supportsStreaming: true,
      supportsTools: true,
      supportsVision: false,
      contextWindow: 128000,
    },
  ],
  mistral: [
    {
      id: 'mistral-large-latest',
      name: 'Mistral Large',
      provider: 'mistral',
      maxTokens: 8192,
      supportsStreaming: true,
      supportsTools: true,
      supportsVision: false,
      contextWindow: 128000,
    },
    {
      id: 'mistral-medium-latest',
      name: 'Mistral Medium',
      provider: 'mistral',
      maxTokens: 8192,
      supportsStreaming: true,
      supportsTools: true,
      supportsVision: false,
      contextWindow: 32000,
    },
  ],
  custom: [],
};

// Default Configuration
export const DEFAULT_AI_CONFIG: AIConfiguration = {
  providers: [],
  defaultParameters: {
    temperature: 0.7,
    maxTokens: 4096,
    topP: 1,
    frequencyPenalty: 0,
    presencePenalty: 0,
  },
};

// Validation Types
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// API Response Types
export interface TestConnectionResult {
  success: boolean;
  error?: string;
  latency?: number;
  modelInfo?: {
    name: string;
    version?: string;
  };
}

// Generation Request Types
export interface GenerationRequest {
  prompt: string;
  providerId?: string;
  modelId?: string;
  parameters?: ModelParameters;
  stream?: boolean;
}

// Generation Response Types
export interface GenerationResponse {
  success: boolean;
  content?: string;
  error?: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}
