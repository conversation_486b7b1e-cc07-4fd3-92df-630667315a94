/**
 * Error handling utilities for opencoder integration
 */

export interface OpencoderError {
  type: 'validation' | 'configuration' | 'execution' | 'network' | 'timeout' | 'unknown';
  message: string;
  originalError?: Error;
  suggestions?: string[];
  recoverable?: boolean;
}

export class OpencoderErrorHandler {
  /**
   * Parse and categorize opencoder errors
   */
  static parseError(error: any): OpencoderError {
    if (!error) {
      return {
        type: 'unknown',
        message: 'An unknown error occurred',
        recoverable: false,
      };
    }

    const errorMessage = error.message || error.toString() || 'Unknown error';
    const lowerMessage = errorMessage.toLowerCase();

    // API Key errors
    if (lowerMessage.includes('api key') || lowerMessage.includes('unauthorized') || lowerMessage.includes('authentication')) {
      return {
        type: 'configuration',
        message: 'Invalid or missing API key',
        originalError: error,
        suggestions: [
          'Check that your API key is correct',
          'Verify the API key has the necessary permissions',
          'Make sure the API key is not expired',
        ],
        recoverable: true,
      };
    }

    // Rate limiting errors
    if (lowerMessage.includes('rate limit') || lowerMessage.includes('too many requests')) {
      return {
        type: 'network',
        message: 'Rate limit exceeded',
        originalError: error,
        suggestions: [
          'Wait a few minutes before trying again',
          'Consider upgrading your API plan for higher limits',
          'Reduce the frequency of requests',
        ],
        recoverable: true,
      };
    }

    // Quota errors
    if (lowerMessage.includes('quota') || lowerMessage.includes('billing') || lowerMessage.includes('usage limit')) {
      return {
        type: 'configuration',
        message: 'API quota exceeded or billing issue',
        originalError: error,
        suggestions: [
          'Check your API usage and billing status',
          'Add payment method or upgrade your plan',
          'Wait for quota reset if on a free tier',
        ],
        recoverable: true,
      };
    }

    // Model errors
    if (lowerMessage.includes('model') && (lowerMessage.includes('not found') || lowerMessage.includes('not available'))) {
      return {
        type: 'configuration',
        message: 'Model not found or not accessible',
        originalError: error,
        suggestions: [
          'Check that the model ID is correct',
          'Verify you have access to this model',
          'Try using a different model',
        ],
        recoverable: true,
      };
    }

    // Network errors
    if (lowerMessage.includes('network') || lowerMessage.includes('connection') || lowerMessage.includes('timeout')) {
      return {
        type: 'network',
        message: 'Network connection error',
        originalError: error,
        suggestions: [
          'Check your internet connection',
          'Try again in a few moments',
          'Verify the API endpoint is accessible',
        ],
        recoverable: true,
      };
    }

    // Timeout errors
    if (lowerMessage.includes('timeout') || lowerMessage.includes('timed out')) {
      return {
        type: 'timeout',
        message: 'Request timed out',
        originalError: error,
        suggestions: [
          'Try with a shorter prompt',
          'Reduce the max tokens parameter',
          'Try again with a simpler request',
        ],
        recoverable: true,
      };
    }

    // Validation errors
    if (lowerMessage.includes('validation') || lowerMessage.includes('invalid') || lowerMessage.includes('required')) {
      return {
        type: 'validation',
        message: 'Invalid request parameters',
        originalError: error,
        suggestions: [
          'Check all required fields are provided',
          'Verify parameter values are within valid ranges',
          'Review the API documentation for correct format',
        ],
        recoverable: true,
      };
    }

    // Opencoder-specific errors
    if (lowerMessage.includes('opencoder')) {
      if (lowerMessage.includes('not found') || lowerMessage.includes('command not found')) {
        return {
          type: 'configuration',
          message: 'Opencoder is not installed or not found',
          originalError: error,
          suggestions: [
            'Install opencoder: npm install -g opencoder',
            'Make sure opencoder is in your PATH',
            'Try restarting the application',
          ],
          recoverable: true,
        };
      }

      if (lowerMessage.includes('config') || lowerMessage.includes('configuration')) {
        return {
          type: 'configuration',
          message: 'Opencoder configuration error',
          originalError: error,
          suggestions: [
            'Check your opencoder configuration file',
            'Verify all required settings are provided',
            'Try resetting to default configuration',
          ],
          recoverable: true,
        };
      }
    }

    // Execution errors
    if (lowerMessage.includes('spawn') || lowerMessage.includes('process') || lowerMessage.includes('exit')) {
      return {
        type: 'execution',
        message: 'Code execution error',
        originalError: error,
        suggestions: [
          'Check system permissions',
          'Verify all dependencies are installed',
          'Try with a simpler request',
        ],
        recoverable: true,
      };
    }

    // Default case
    return {
      type: 'unknown',
      message: errorMessage,
      originalError: error,
      suggestions: [
        'Try again in a few moments',
        'Check your configuration settings',
        'Contact support if the problem persists',
      ],
      recoverable: false,
    };
  }

  /**
   * Get user-friendly error message
   */
  static getUserFriendlyMessage(error: OpencoderError): string {
    const baseMessage = error.message;
    
    if (error.suggestions && error.suggestions.length > 0) {
      return `${baseMessage}\n\nSuggestions:\n${error.suggestions.map(s => `• ${s}`).join('\n')}`;
    }

    return baseMessage;
  }

  /**
   * Check if error is recoverable
   */
  static isRecoverable(error: OpencoderError): boolean {
    return error.recoverable === true;
  }

  /**
   * Get retry delay based on error type
   */
  static getRetryDelay(error: OpencoderError): number {
    switch (error.type) {
      case 'network':
        return 5000; // 5 seconds
      case 'timeout':
        return 10000; // 10 seconds
      case 'configuration':
        return 0; // No automatic retry for config errors
      case 'validation':
        return 0; // No automatic retry for validation errors
      default:
        return 3000; // 3 seconds
    }
  }

  /**
   * Create error response for API
   */
  static createErrorResponse(error: any, statusCode: number = 500) {
    const parsedError = this.parseError(error);
    
    return {
      success: false,
      error: parsedError.message,
      errorType: parsedError.type,
      suggestions: parsedError.suggestions,
      recoverable: parsedError.recoverable,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Log error with appropriate level
   */
  static logError(error: OpencoderError, context?: string) {
    const logMessage = context ? `[${context}] ${error.message}` : error.message;
    
    switch (error.type) {
      case 'configuration':
      case 'validation':
        console.warn(logMessage, error.originalError);
        break;
      case 'network':
      case 'timeout':
        console.info(logMessage, error.originalError);
        break;
      default:
        console.error(logMessage, error.originalError);
    }
  }

  /**
   * Handle error with automatic retry logic
   */
  static async handleWithRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    context?: string
  ): Promise<T> {
    let lastError: OpencoderError | null = null;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = this.parseError(error);
        this.logError(lastError, context);
        
        if (!this.isRecoverable(lastError) || attempt === maxRetries) {
          throw lastError;
        }
        
        const delay = this.getRetryDelay(lastError);
        if (delay > 0) {
          console.info(`Retrying in ${delay}ms (attempt ${attempt}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw lastError;
  }
}
