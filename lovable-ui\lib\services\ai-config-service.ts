import { AIConfiguration, ProviderConfig, DEFAULT_AI_CONFIG } from '../types/ai-config';

export class AIConfigService {
  private static instance: AIConfigService;
  private config: AIConfiguration = DEFAULT_AI_CONFIG;

  private constructor() {
    this.loadConfig();
  }

  static getInstance(): AIConfigService {
    if (!AIConfigService.instance) {
      AIConfigService.instance = new AIConfigService();
    }
    return AIConfigService.instance;
  }

  /**
   * Load configuration from storage or environment variables
   */
  private loadConfig(): void {
    try {
      // Try to load from localStorage first (client-side)
      if (typeof window !== 'undefined') {
        const saved = localStorage.getItem('ai-config');
        if (saved) {
          this.config = JSON.parse(saved);
          return;
        }
      }

      // Fallback to environment variables for server-side
      this.loadFromEnvironment();
    } catch (error) {
      console.error('Failed to load AI config:', error);
      this.loadFromEnvironment();
    }
  }

  /**
   * Load configuration from environment variables as fallback
   */
  private loadFromEnvironment(): void {
    const providers: ProviderConfig[] = [];

    // Check for Anthropic API key
    if (process.env.ANTHROPIC_API_KEY) {
      providers.push({
        id: 'anthropic-env',
        name: 'Anthropic (Environment)',
        provider: 'anthropic',
        apiKey: process.env.ANTHROPIC_API_KEY,
        enabled: true,
        isDefault: true,
      });
    }

    // Check for OpenAI API key
    if (process.env.OPENAI_API_KEY) {
      providers.push({
        id: 'openai-env',
        name: 'OpenAI (Environment)',
        provider: 'openai',
        apiKey: process.env.OPENAI_API_KEY,
        enabled: true,
        isDefault: !providers.length, // Make default if no other providers
      });
    }

    // Check for Google API key
    if (process.env.GOOGLE_API_KEY) {
      providers.push({
        id: 'google-env',
        name: 'Google (Environment)',
        provider: 'google',
        apiKey: process.env.GOOGLE_API_KEY,
        enabled: true,
        isDefault: !providers.length,
      });
    }

    if (providers.length > 0) {
      this.config = {
        ...DEFAULT_AI_CONFIG,
        providers,
        defaultProvider: providers.find(p => p.isDefault)?.id,
      };
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): AIConfiguration {
    return this.config;
  }

  /**
   * Update configuration
   */
  setConfig(config: AIConfiguration): void {
    this.config = config;
    this.saveConfig();
  }

  /**
   * Save configuration to storage
   */
  private saveConfig(): void {
    try {
      if (typeof window !== 'undefined') {
        localStorage.setItem('ai-config', JSON.stringify(this.config));
      }
    } catch (error) {
      console.error('Failed to save AI config:', error);
    }
  }

  /**
   * Get default provider configuration
   */
  getDefaultProvider(): ProviderConfig | null {
    if (!this.config.defaultProvider) {
      // Return first enabled provider as fallback
      return this.config.providers.find(p => p.enabled) || null;
    }
    
    return this.config.providers.find(p => p.id === this.config.defaultProvider) || null;
  }

  /**
   * Get provider by ID
   */
  getProvider(id: string): ProviderConfig | null {
    return this.config.providers.find(p => p.id === id) || null;
  }

  /**
   * Get all enabled providers
   */
  getEnabledProviders(): ProviderConfig[] {
    return this.config.providers.filter(p => p.enabled);
  }

  /**
   * Get default model for the default provider
   */
  getDefaultModel(): string | null {
    if (this.config.defaultModel) {
      return this.config.defaultModel;
    }

    const defaultProvider = this.getDefaultProvider();
    if (!defaultProvider) {
      return null;
    }

    // Return a sensible default based on provider
    switch (defaultProvider.provider) {
      case 'openai':
        return 'gpt-4o-mini';
      case 'anthropic':
        return 'claude-3-5-haiku-20241022';
      case 'google':
        return 'gemini-1.5-flash';
      case 'cohere':
        return 'command-r';
      case 'mistral':
        return 'mistral-medium-latest';
      case 'custom':
        return (defaultProvider as any).modelName || 'gpt-3.5-turbo';
      default:
        return null;
    }
  }

  /**
   * Check if any providers are configured
   */
  hasProviders(): boolean {
    return this.config.providers.length > 0;
  }

  /**
   * Check if any providers are enabled
   */
  hasEnabledProviders(): boolean {
    return this.config.providers.some(p => p.enabled);
  }

  /**
   * Get configuration for backward compatibility with existing code
   */
  getLegacyConfig(): {
    hasAnthropicKey: boolean;
    anthropicKey?: string;
    hasOpenAIKey: boolean;
    openaiKey?: string;
    defaultProvider: ProviderConfig | null;
    defaultModel: string | null;
  } {
    const anthropicProvider = this.config.providers.find(p => p.provider === 'anthropic' && p.enabled);
    const openaiProvider = this.config.providers.find(p => p.provider === 'openai' && p.enabled);

    return {
      hasAnthropicKey: !!anthropicProvider,
      anthropicKey: anthropicProvider?.apiKey,
      hasOpenAIKey: !!openaiProvider,
      openaiKey: openaiProvider?.apiKey,
      defaultProvider: this.getDefaultProvider(),
      defaultModel: this.getDefaultModel(),
    };
  }
}
