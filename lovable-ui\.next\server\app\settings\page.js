/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/settings/page";
exports.ids = ["app/settings/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'settings',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/settings/page.tsx */ \"(rsc)/./app/settings/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/settings/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/settings/page\",\n        pathname: \"/settings\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/settings/page.tsx */ \"(ssr)/./app/settings/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNEb2N1bWVudHMlNUMlNUNnaXRodWIlNUMlNUNsb3ZhYmxlLWNsb25lJTVDJTVDbG92YWJsZS11aSU1QyU1Q2FwcCU1QyU1Q3NldHRpbmdzJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUEySCIsInNvdXJjZXMiOlsid2VicGFjazovL2xvdmFibGUtdWkvPzUyNjEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxVc2VyXFxcXERvY3VtZW50c1xcXFxnaXRodWJcXFxcbG92YWJsZS1jbG9uZVxcXFxsb3ZhYmxlLXVpXFxcXGFwcFxcXFxzZXR0aW5nc1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Clib%5C%5Ccontexts%5C%5Cai-config-context.tsx%22%2C%22ids%22%3A%5B%22AIConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Clib%5C%5Ccontexts%5C%5Cai-config-context.tsx%22%2C%22ids%22%3A%5B%22AIConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/contexts/ai-config-context.tsx */ \"(ssr)/./lib/contexts/ai-config-context.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNEb2N1bWVudHMlNUMlNUNnaXRodWIlNUMlNUNsb3ZhYmxlLWNsb25lJTVDJTVDbG92YWJsZS11aSU1QyU1Q2xpYiU1QyU1Q2NvbnRleHRzJTVDJTVDYWktY29uZmlnLWNvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQUlDb25maWdQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNVc2VyJTVDJTVDRG9jdW1lbnRzJTVDJTVDZ2l0aHViJTVDJTVDbG92YWJsZS1jbG9uZSU1QyU1Q2xvdmFibGUtdWklNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDVXNlciU1QyU1Q0RvY3VtZW50cyU1QyU1Q2dpdGh1YiU1QyU1Q2xvdmFibGUtY2xvbmUlNUMlNUNsb3ZhYmxlLXVpJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUE4SyIsInNvdXJjZXMiOlsid2VicGFjazovL2xvdmFibGUtdWkvP2U5MDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBSUNvbmZpZ1Byb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcVXNlclxcXFxEb2N1bWVudHNcXFxcZ2l0aHViXFxcXGxvdmFibGUtY2xvbmVcXFxcbG92YWJsZS11aVxcXFxsaWJcXFxcY29udGV4dHNcXFxcYWktY29uZmlnLWNvbnRleHQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Clib%5C%5Ccontexts%5C%5Cai-config-context.tsx%22%2C%22ids%22%3A%5B%22AIConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/settings/page.tsx":
/*!*******************************!*\
  !*** ./app/settings/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar */ \"(ssr)/./components/Navbar.tsx\");\n/* harmony import */ var _lib_contexts_ai_config_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/contexts/ai-config-context */ \"(ssr)/./lib/contexts/ai-config-context.tsx\");\n/* harmony import */ var _components_ai_config_ProviderCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ai-config/ProviderCard */ \"(ssr)/./components/ai-config/ProviderCard.tsx\");\n/* harmony import */ var _components_ai_config_AddProviderModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ai-config/AddProviderModal */ \"(ssr)/./components/ai-config/AddProviderModal.tsx\");\n/* harmony import */ var _components_ai_config_DefaultSettingsCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ai-config/DefaultSettingsCard */ \"(ssr)/./components/ai-config/DefaultSettingsCard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction SettingsPage() {\n    const { state, actions } = (0,_lib_contexts_ai_config_context__WEBPACK_IMPORTED_MODULE_3__.useAIConfig)();\n    const [showAddProvider, setShowAddProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProvider, setEditingProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleAddProvider = (provider)=>{\n        actions.addProvider(provider);\n        setShowAddProvider(false);\n    };\n    const handleEditProvider = (provider)=>{\n        setEditingProvider(provider);\n        setShowAddProvider(true);\n    };\n    const handleUpdateProvider = (provider)=>{\n        if (editingProvider) {\n            actions.updateProvider(editingProvider.id, provider);\n            setEditingProvider(null);\n            setShowAddProvider(false);\n        }\n    };\n    const handleDeleteProvider = (id)=>{\n        if (confirm(\"Are you sure you want to delete this provider?\")) {\n            actions.removeProvider(id);\n        }\n    };\n    const handleTestConnection = async (id)=>{\n        return await actions.testConnection(id);\n    };\n    const handleToggleProvider = (id)=>{\n        actions.toggleProvider(id);\n    };\n    const handleSetDefault = (id)=>{\n        actions.setDefaultProvider(id);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-16\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8 max-w-6xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-white mb-2\",\n                                children: \"AI Configuration\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Configure AI providers, models, and default settings for code generation.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_config_DefaultSettingsCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 rounded-lg border border-gray-800 p-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-white mb-1\",\n                                                children: \"AI Providers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Add and configure AI providers for code generation.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddProvider(true),\n                                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                        children: \"Add Provider\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this),\n                            state.config.providers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-8 h-8 text-gray-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-white mb-2\",\n                                        children: \"No providers configured\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"Add your first AI provider to start generating code.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddProvider(true),\n                                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors\",\n                                        children: \"Add Provider\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: state.config.providers.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_config_ProviderCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        provider: provider,\n                                        isDefault: state.config.defaultProvider === provider.id,\n                                        onEdit: ()=>handleEditProvider(provider),\n                                        onDelete: ()=>handleDeleteProvider(provider.id),\n                                        onTest: ()=>handleTestConnection(provider.id),\n                                        onToggle: ()=>handleToggleProvider(provider.id),\n                                        onSetDefault: ()=>handleSetDefault(provider.id)\n                                    }, provider.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 rounded-lg border border-gray-800 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Configuration Status\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-800 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-white mb-1\",\n                                                children: state.config.providers.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Total Providers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-800 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-white mb-1\",\n                                                children: state.config.providers.filter((p)=>p.enabled).length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Active Providers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-800 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-white mb-1\",\n                                                children: state.config.defaultProvider ? \"✓\" : \"✗\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Default Set\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            showAddProvider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_config_AddProviderModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                provider: editingProvider,\n                onSave: editingProvider ? handleUpdateProvider : handleAddProvider,\n                onCancel: ()=>{\n                    setShowAddProvider(false);\n                    setEditingProvider(null);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 149,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/settings/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Navbar() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"absolute top-0 left-0 right-0 z-20 flex items-center justify-between px-6 py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/\",\n                        className: \"flex items-center gap-2 text-2xl font-semibold text-white hover:opacity-90 transition-opacity\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-block w-6 h-6 rounded-sm bg-gradient-to-br from-orange-400 via-pink-500 to-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 13,\n                                columnNumber: 11\n                            }, this),\n                            \"Lovable\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center gap-8 text-sm text-gray-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"hover:text-white transition-colors\",\n                                children: \"Community\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"hover:text-white transition-colors\",\n                                children: \"Enterprise\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"hover:text-white transition-colors\",\n                                children: \"Learn\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"hover:text-white transition-colors\",\n                                children: \"Shipped\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4 text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/settings\",\n                        className: \"text-gray-300 hover:text-white transition-colors flex items-center gap-1\",\n                        title: \"AI Settings\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this),\n                            \"Settings\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"#\",\n                        className: \"text-gray-300 hover:text-white transition-colors\",\n                        children: \"Log in\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"#\",\n                        className: \"px-4 py-2 bg-white text-black rounded-lg font-semibold hover:bg-gray-100 transition-colors\",\n                        children: \"Get started\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ai-config/AddProviderModal.tsx":
/*!***************************************************!*\
  !*** ./components/ai-config/AddProviderModal.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddProviderModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AddProviderModal({ provider, onSave, onCancel }) {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        provider: \"openai\",\n        apiKey: \"\",\n        baseURL: \"\",\n        organization: \"\",\n        project: \"\",\n        modelName: \"\",\n        headers: {},\n        enabled: true\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (provider) {\n            setFormData({\n                name: provider.name,\n                provider: provider.provider,\n                apiKey: provider.apiKey,\n                baseURL: provider.baseURL || \"\",\n                organization: provider.organization || \"\",\n                project: provider.project || \"\",\n                modelName: provider.modelName || \"\",\n                headers: provider.headers || {},\n                enabled: provider.enabled\n            });\n        }\n    }, [\n        provider\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.name.trim()) {\n            newErrors.name = \"Provider name is required\";\n        }\n        if (!formData.apiKey.trim()) {\n            newErrors.apiKey = \"API key is required\";\n        }\n        if (formData.provider === \"custom\") {\n            if (!formData.baseURL.trim()) {\n                newErrors.baseURL = \"Base URL is required for custom providers\";\n            }\n            if (!formData.modelName.trim()) {\n                newErrors.modelName = \"Model name is required for custom providers\";\n            }\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        const newProvider = {\n            id: provider?.id || `${formData.provider}-${Date.now()}`,\n            name: formData.name,\n            provider: formData.provider,\n            apiKey: formData.apiKey,\n            enabled: formData.enabled,\n            ...formData.baseURL && {\n                baseURL: formData.baseURL\n            },\n            ...formData.organization && {\n                organization: formData.organization\n            },\n            ...formData.project && {\n                project: formData.project\n            },\n            ...formData.modelName && {\n                modelName: formData.modelName\n            },\n            ...Object.keys(formData.headers).length > 0 && {\n                headers: formData.headers\n            }\n        };\n        onSave(newProvider);\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-900 rounded-lg border border-gray-800 w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-gray-800\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-white\",\n                        children: provider ? \"Edit Provider\" : \"Add AI Provider\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"p-6 space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Provider Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: formData.provider,\n                                    onChange: (e)=>handleInputChange(\"provider\", e.target.value),\n                                    className: \"w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                    disabled: !!provider,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"openai\",\n                                            children: \"OpenAI\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"anthropic\",\n                                            children: \"Anthropic\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"google\",\n                                            children: \"Google\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"cohere\",\n                                            children: \"Cohere\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"mistral\",\n                                            children: \"Mistral\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"custom\",\n                                            children: \"Custom (OpenAI-compatible)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Provider Name\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.name,\n                                    onChange: (e)=>handleInputChange(\"name\", e.target.value),\n                                    className: `w-full bg-gray-800 border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.name ? \"border-red-500\" : \"border-gray-700\"}`,\n                                    placeholder: \"e.g., My OpenAI Account\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this),\n                                errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-400 text-sm mt-1\",\n                                    children: errors.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"API Key\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    value: formData.apiKey,\n                                    onChange: (e)=>handleInputChange(\"apiKey\", e.target.value),\n                                    className: `w-full bg-gray-800 border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.apiKey ? \"border-red-500\" : \"border-gray-700\"}`,\n                                    placeholder: \"Enter your API key\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                errors.apiKey && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-400 text-sm mt-1\",\n                                    children: errors.apiKey\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 31\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this),\n                        (formData.provider === \"custom\" || formData.baseURL) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: [\n                                        \"Base URL \",\n                                        formData.provider === \"custom\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-400\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 61\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"url\",\n                                    value: formData.baseURL,\n                                    onChange: (e)=>handleInputChange(\"baseURL\", e.target.value),\n                                    className: `w-full bg-gray-800 border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.baseURL ? \"border-red-500\" : \"border-gray-700\"}`,\n                                    placeholder: \"https://api.example.com/v1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this),\n                                errors.baseURL && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-400 text-sm mt-1\",\n                                    children: errors.baseURL\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 34\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this),\n                        formData.provider === \"custom\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: [\n                                        \"Model Name \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-400\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 28\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.modelName,\n                                    onChange: (e)=>handleInputChange(\"modelName\", e.target.value),\n                                    className: `w-full bg-gray-800 border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.modelName ? \"border-red-500\" : \"border-gray-700\"}`,\n                                    placeholder: \"e.g., gpt-3.5-turbo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, this),\n                                errors.modelName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-400 text-sm mt-1\",\n                                    children: errors.modelName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 36\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, this),\n                        formData.provider === \"openai\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                            children: \"Organization ID (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.organization,\n                                            onChange: (e)=>handleInputChange(\"organization\", e.target.value),\n                                            className: \"w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"org-...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                            children: \"Project ID (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.project,\n                                            onChange: (e)=>handleInputChange(\"project\", e.target.value),\n                                            className: \"w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"proj_...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300\",\n                                    children: \"Enable Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>handleInputChange(\"enabled\", !formData.enabled),\n                                    className: `w-12 h-6 rounded-full transition-colors ${formData.enabled ? \"bg-green-600\" : \"bg-gray-600\"}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-5 h-5 bg-white rounded-full transition-transform ${formData.enabled ? \"translate-x-6\" : \"translate-x-1\"}`\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-end space-x-3 pt-6 border-t border-gray-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onCancel,\n                                    className: \"px-4 py-2 text-gray-400 hover:text-white transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors\",\n                                    children: provider ? \"Update Provider\" : \"Add Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AddProviderModal.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ai-config/AddProviderModal.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ai-config/DefaultSettingsCard.tsx":
/*!******************************************************!*\
  !*** ./components/ai-config/DefaultSettingsCard.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DefaultSettingsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_contexts_ai_config_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/contexts/ai-config-context */ \"(ssr)/./lib/contexts/ai-config-context.tsx\");\n/* harmony import */ var _lib_types_ai_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/types/ai-config */ \"(ssr)/./lib/types/ai-config.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction DefaultSettingsCard() {\n    const { state, actions } = (0,_lib_contexts_ai_config_context__WEBPACK_IMPORTED_MODULE_2__.useAIConfig)();\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tempParams, setTempParams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(state.config.defaultParameters);\n    const handleSave = ()=>{\n        actions.updateDefaultParameters(tempParams);\n        setIsEditing(false);\n    };\n    const handleCancel = ()=>{\n        setTempParams(state.config.defaultParameters);\n        setIsEditing(false);\n    };\n    const getAvailableModels = ()=>{\n        const enabledProviders = state.config.providers.filter((p)=>p.enabled);\n        const models = [];\n        enabledProviders.forEach((provider)=>{\n            const providerModels = _lib_types_ai_config__WEBPACK_IMPORTED_MODULE_3__.AVAILABLE_MODELS[provider.provider] || [];\n            providerModels.forEach((model)=>{\n                models.push({\n                    id: model.id,\n                    name: `${model.name} (${provider.name})`,\n                    provider: provider.id\n                });\n            });\n        });\n        return models;\n    };\n    const availableModels = getAvailableModels();\n    const defaultProvider = state.config.providers.find((p)=>p.id === state.config.defaultProvider);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900 rounded-lg border border-gray-800 p-6 mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-white mb-1\",\n                                children: \"Default Settings\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"Configure default AI model and parameters for code generation.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setIsEditing(!isEditing),\n                        className: \"text-gray-400 hover:text-white transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-white\",\n                                children: \"Provider & Model\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                        children: \"Default Provider\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: state.config.defaultProvider || \"\",\n                                        onChange: (e)=>actions.setDefaultProvider(e.target.value),\n                                        className: \"w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        disabled: state.config.providers.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select a provider\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 15\n                                            }, this),\n                                            state.config.providers.filter((p)=>p.enabled).map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: provider.id,\n                                                    children: provider.name\n                                                }, provider.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                        children: \"Default Model\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: state.config.defaultModel || \"\",\n                                        onChange: (e)=>actions.setDefaultModel(e.target.value),\n                                        className: \"w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        disabled: availableModels.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select a model\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 15\n                                            }, this),\n                                            availableModels.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: model.id,\n                                                    children: model.name\n                                                }, `${model.provider}-${model.id}`, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-white\",\n                                children: \"Model Parameters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    \"Temperature (\",\n                                                    tempParams.temperature,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"range\",\n                                                min: \"0\",\n                                                max: \"2\",\n                                                step: \"0.1\",\n                                                value: tempParams.temperature,\n                                                onChange: (e)=>setTempParams((prev)=>({\n                                                            ...prev,\n                                                            temperature: parseFloat(e.target.value)\n                                                        })),\n                                                className: \"w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Max Tokens\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"1\",\n                                                max: \"32000\",\n                                                value: tempParams.maxTokens,\n                                                onChange: (e)=>setTempParams((prev)=>({\n                                                            ...prev,\n                                                            maxTokens: parseInt(e.target.value)\n                                                        })),\n                                                className: \"w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: [\n                                                    \"Top P (\",\n                                                    tempParams.topP,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"range\",\n                                                min: \"0\",\n                                                max: \"1\",\n                                                step: \"0.1\",\n                                                value: tempParams.topP,\n                                                onChange: (e)=>setTempParams((prev)=>({\n                                                            ...prev,\n                                                            topP: parseFloat(e.target.value)\n                                                        })),\n                                                className: \"w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleSave,\n                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                                children: \"Save\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleCancel,\n                                                className: \"bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Temperature:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: state.config.defaultParameters.temperature\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Max Tokens:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: state.config.defaultParameters.maxTokens\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Top P:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: state.config.defaultParameters.topP\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 pt-6 border-t border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-400\",\n                            children: \"Current Configuration:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-white\",\n                            children: defaultProvider ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    defaultProvider.name,\n                                    state.config.defaultModel && ` • ${state.config.defaultModel}`\n                                ]\n                            }, void 0, true) : \"No default provider set\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\DefaultSettingsCard.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ai-config/DefaultSettingsCard.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ai-config/ProviderCard.tsx":
/*!***********************************************!*\
  !*** ./components/ai-config/ProviderCard.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProviderCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ProviderCard({ provider, isDefault, onEdit, onDelete, onTest, onToggle, onSetDefault }) {\n    const [testing, setTesting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleTest = async ()=>{\n        setTesting(true);\n        setTestResult(null);\n        try {\n            const result = await onTest();\n            setTestResult(result);\n        } catch (error) {\n            setTestResult({\n                success: false,\n                error: error instanceof Error ? error.message : \"Test failed\"\n            });\n        } finally{\n            setTesting(false);\n        }\n    };\n    const getProviderIcon = (providerType)=>{\n        switch(providerType){\n            case \"openai\":\n                return \"\\uD83E\\uDD16\";\n            case \"anthropic\":\n                return \"\\uD83E\\uDDE0\";\n            case \"google\":\n                return \"\\uD83D\\uDD0D\";\n            case \"cohere\":\n                return \"\\uD83D\\uDCAC\";\n            case \"mistral\":\n                return \"\\uD83C\\uDF2A️\";\n            case \"custom\":\n                return \"⚙️\";\n            default:\n                return \"\\uD83E\\uDD16\";\n        }\n    };\n    const getProviderDisplayName = (providerType)=>{\n        switch(providerType){\n            case \"openai\":\n                return \"OpenAI\";\n            case \"anthropic\":\n                return \"Anthropic\";\n            case \"google\":\n                return \"Google\";\n            case \"cohere\":\n                return \"Cohere\";\n            case \"mistral\":\n                return \"Mistral\";\n            case \"custom\":\n                return \"Custom\";\n            default:\n                return providerType;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-800 rounded-lg border border-gray-700 p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl\",\n                                children: getProviderIcon(provider.provider)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: provider.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 15\n                                            }, this),\n                                            isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-blue-600 text-white text-xs px-2 py-1 rounded-full\",\n                                                children: \"Default\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this),\n                                            !provider.enabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gray-600 text-white text-xs px-2 py-1 rounded-full\",\n                                                children: \"Disabled\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: [\n                                            getProviderDisplayName(provider.provider),\n                                            provider.provider === \"custom\" && provider.baseURL && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-1\",\n                                                children: [\n                                                    \"• \",\n                                                    new URL(provider.baseURL).hostname\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onToggle,\n                            className: `w-12 h-6 rounded-full transition-colors ${provider.enabled ? \"bg-green-600\" : \"bg-gray-600\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `w-5 h-5 bg-white rounded-full transition-transform ${provider.enabled ? \"translate-x-6\" : \"translate-x-1\"}`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-400\",\n                            children: \"API Key:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-400\",\n                            children: provider.apiKey ? \"••••••••\" : \"Not set\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `mb-4 p-3 rounded-lg text-sm ${testResult.success ? \"bg-green-900 border border-green-700 text-green-100\" : \"bg-red-900 border border-red-700 text-red-100\"}`,\n                children: testResult.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium\",\n                            children: \"✓ Connection successful\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 15\n                        }, this),\n                        testResult.latency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs opacity-75 mt-1\",\n                            children: [\n                                \"Response time: \",\n                                testResult.latency,\n                                \"ms\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 17\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 13\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium\",\n                            children: \"✗ Connection failed\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 15\n                        }, this),\n                        testResult.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs opacity-75 mt-1\",\n                            children: testResult.error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 17\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleTest,\n                                disabled: testing || !provider.enabled,\n                                className: \"bg-gray-700 hover:bg-gray-600 disabled:bg-gray-800 disabled:text-gray-500 text-white px-3 py-1 rounded text-sm font-medium transition-colors\",\n                                children: testing ? \"Testing...\" : \"Test\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            !isDefault && provider.enabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onSetDefault,\n                                className: \"bg-blue-700 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm font-medium transition-colors\",\n                                children: \"Set Default\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onEdit,\n                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                title: \"Edit provider\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onDelete,\n                                className: \"text-gray-400 hover:text-red-400 transition-colors\",\n                                title: \"Delete provider\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderCard.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ai-config/ProviderCard.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/contexts/ai-config-context.tsx":
/*!********************************************!*\
  !*** ./lib/contexts/ai-config-context.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIConfigProvider: () => (/* binding */ AIConfigProvider),\n/* harmony export */   useAIConfig: () => (/* binding */ useAIConfig)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types_ai_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../types/ai-config */ \"(ssr)/./lib/types/ai-config.ts\");\n/* __next_internal_client_entry_do_not_use__ AIConfigProvider,useAIConfig auto */ \n\n\n// Reducer\nfunction aiConfigReducer(state, action) {\n    switch(action.type){\n        case \"SET_CONFIG\":\n            return {\n                ...state,\n                config: action.payload,\n                error: null\n            };\n        case \"ADD_PROVIDER\":\n            return {\n                ...state,\n                config: {\n                    ...state.config,\n                    providers: [\n                        ...state.config.providers,\n                        action.payload\n                    ]\n                }\n            };\n        case \"UPDATE_PROVIDER\":\n            return {\n                ...state,\n                config: {\n                    ...state.config,\n                    providers: state.config.providers.map((provider)=>provider.id === action.payload.id ? {\n                            ...provider,\n                            ...action.payload.config\n                        } : provider)\n                }\n            };\n        case \"REMOVE_PROVIDER\":\n            const updatedProviders = state.config.providers.filter((p)=>p.id !== action.payload);\n            return {\n                ...state,\n                config: {\n                    ...state.config,\n                    providers: updatedProviders,\n                    // Reset default provider if it was removed\n                    defaultProvider: state.config.defaultProvider === action.payload ? updatedProviders[0]?.id : state.config.defaultProvider\n                }\n            };\n        case \"SET_DEFAULT_PROVIDER\":\n            return {\n                ...state,\n                config: {\n                    ...state.config,\n                    defaultProvider: action.payload\n                }\n            };\n        case \"SET_DEFAULT_MODEL\":\n            return {\n                ...state,\n                config: {\n                    ...state.config,\n                    defaultModel: action.payload\n                }\n            };\n        case \"UPDATE_DEFAULT_PARAMETERS\":\n            return {\n                ...state,\n                config: {\n                    ...state.config,\n                    defaultParameters: {\n                        ...state.config.defaultParameters,\n                        ...action.payload\n                    }\n                }\n            };\n        case \"TOGGLE_PROVIDER\":\n            return {\n                ...state,\n                config: {\n                    ...state.config,\n                    providers: state.config.providers.map((provider)=>provider.id === action.payload ? {\n                            ...provider,\n                            enabled: !provider.enabled\n                        } : provider)\n                }\n            };\n        case \"RESET_CONFIG\":\n            return {\n                ...state,\n                config: _types_ai_config__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_AI_CONFIG,\n                error: null\n            };\n        default:\n            return state;\n    }\n}\n// Initial State\nconst initialState = {\n    config: _types_ai_config__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_AI_CONFIG,\n    isLoading: false,\n    error: null\n};\n// Context\nconst AIConfigContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Storage Key\nconst STORAGE_KEY = \"ai-config\";\nfunction AIConfigProvider({ children }) {\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(aiConfigReducer, initialState);\n    // Load config on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadConfig();\n    }, []);\n    // Save config whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (state.config !== _types_ai_config__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_AI_CONFIG) {\n            saveConfig();\n        }\n    }, [\n        state.config\n    ]);\n    const saveConfig = async ()=>{\n        try {\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(state.config));\n        } catch (error) {\n            console.error(\"Failed to save AI config:\", error);\n        }\n    };\n    const loadConfig = async ()=>{\n        try {\n            const saved = localStorage.getItem(STORAGE_KEY);\n            if (saved) {\n                const config = JSON.parse(saved);\n                dispatch({\n                    type: \"SET_CONFIG\",\n                    payload: config\n                });\n            }\n        } catch (error) {\n            console.error(\"Failed to load AI config:\", error);\n        }\n    };\n    const testConnection = async (providerId)=>{\n        const provider = state.config.providers.find((p)=>p.id === providerId);\n        if (!provider) {\n            return {\n                success: false,\n                error: \"Provider not found\"\n            };\n        }\n        try {\n            const response = await fetch(\"/api/ai/test-connection\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    provider\n                })\n            });\n            return await response.json();\n        } catch (error) {\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : \"Connection test failed\"\n            };\n        }\n    };\n    const validateProvider = (provider)=>{\n        const errors = [];\n        if (!provider.name.trim()) {\n            errors.push(\"Provider name is required\");\n        }\n        if (!provider.apiKey.trim()) {\n            errors.push(\"API key is required\");\n        }\n        if (provider.provider === \"custom\") {\n            const customProvider = provider;\n            if (!customProvider.baseURL?.trim()) {\n                errors.push(\"Base URL is required for custom providers\");\n            }\n            if (!customProvider.modelName?.trim()) {\n                errors.push(\"Model name is required for custom providers\");\n            }\n        }\n        return {\n            isValid: errors.length === 0,\n            errors\n        };\n    };\n    const actions = {\n        setConfig: (config)=>dispatch({\n                type: \"SET_CONFIG\",\n                payload: config\n            }),\n        addProvider: (provider)=>dispatch({\n                type: \"ADD_PROVIDER\",\n                payload: provider\n            }),\n        updateProvider: (id, updates)=>dispatch({\n                type: \"UPDATE_PROVIDER\",\n                payload: {\n                    id,\n                    config: updates\n                }\n            }),\n        removeProvider: (id)=>dispatch({\n                type: \"REMOVE_PROVIDER\",\n                payload: id\n            }),\n        setDefaultProvider: (providerId)=>dispatch({\n                type: \"SET_DEFAULT_PROVIDER\",\n                payload: providerId\n            }),\n        setDefaultModel: (modelId)=>dispatch({\n                type: \"SET_DEFAULT_MODEL\",\n                payload: modelId\n            }),\n        updateDefaultParameters: (parameters)=>dispatch({\n                type: \"UPDATE_DEFAULT_PARAMETERS\",\n                payload: parameters\n            }),\n        toggleProvider: (id)=>dispatch({\n                type: \"TOGGLE_PROVIDER\",\n                payload: id\n            }),\n        resetConfig: ()=>dispatch({\n                type: \"RESET_CONFIG\"\n            }),\n        saveConfig,\n        loadConfig,\n        testConnection,\n        validateProvider\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIConfigContext.Provider, {\n        value: {\n            state,\n            actions\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\lib\\\\contexts\\\\ai-config-context.tsx\",\n        lineNumber: 274,\n        columnNumber: 5\n    }, this);\n}\n// Hook\nfunction useAIConfig() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AIConfigContext);\n    if (context === undefined) {\n        throw new Error(\"useAIConfig must be used within an AIConfigProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/contexts/ai-config-context.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/types/ai-config.ts":
/*!********************************!*\
  !*** ./lib/types/ai-config.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AVAILABLE_MODELS: () => (/* binding */ AVAILABLE_MODELS),\n/* harmony export */   DEFAULT_AI_CONFIG: () => (/* binding */ DEFAULT_AI_CONFIG)\n/* harmony export */ });\n// AI Provider Types\n// Available Models by Provider\nconst AVAILABLE_MODELS = {\n    openai: [\n        {\n            id: \"gpt-4o\",\n            name: \"GPT-4o\",\n            provider: \"openai\",\n            maxTokens: 4096,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 128000\n        },\n        {\n            id: \"gpt-4o-mini\",\n            name: \"GPT-4o Mini\",\n            provider: \"openai\",\n            maxTokens: 16384,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 128000\n        },\n        {\n            id: \"gpt-4-turbo\",\n            name: \"GPT-4 Turbo\",\n            provider: \"openai\",\n            maxTokens: 4096,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 128000\n        },\n        {\n            id: \"gpt-3.5-turbo\",\n            name: \"GPT-3.5 Turbo\",\n            provider: \"openai\",\n            maxTokens: 4096,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: false,\n            contextWindow: 16385\n        }\n    ],\n    anthropic: [\n        {\n            id: \"claude-3-5-sonnet-20241022\",\n            name: \"Claude 3.5 Sonnet\",\n            provider: \"anthropic\",\n            maxTokens: 8192,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 200000\n        },\n        {\n            id: \"claude-3-5-haiku-20241022\",\n            name: \"Claude 3.5 Haiku\",\n            provider: \"anthropic\",\n            maxTokens: 8192,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 200000\n        },\n        {\n            id: \"claude-3-opus-20240229\",\n            name: \"Claude 3 Opus\",\n            provider: \"anthropic\",\n            maxTokens: 4096,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 200000\n        }\n    ],\n    google: [\n        {\n            id: \"gemini-1.5-pro\",\n            name: \"Gemini 1.5 Pro\",\n            provider: \"google\",\n            maxTokens: 8192,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 2000000\n        },\n        {\n            id: \"gemini-1.5-flash\",\n            name: \"Gemini 1.5 Flash\",\n            provider: \"google\",\n            maxTokens: 8192,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: true,\n            contextWindow: 1000000\n        }\n    ],\n    cohere: [\n        {\n            id: \"command-r-plus\",\n            name: \"Command R+\",\n            provider: \"cohere\",\n            maxTokens: 4096,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: false,\n            contextWindow: 128000\n        },\n        {\n            id: \"command-r\",\n            name: \"Command R\",\n            provider: \"cohere\",\n            maxTokens: 4096,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: false,\n            contextWindow: 128000\n        }\n    ],\n    mistral: [\n        {\n            id: \"mistral-large-latest\",\n            name: \"Mistral Large\",\n            provider: \"mistral\",\n            maxTokens: 8192,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: false,\n            contextWindow: 128000\n        },\n        {\n            id: \"mistral-medium-latest\",\n            name: \"Mistral Medium\",\n            provider: \"mistral\",\n            maxTokens: 8192,\n            supportsStreaming: true,\n            supportsTools: true,\n            supportsVision: false,\n            contextWindow: 32000\n        }\n    ],\n    custom: []\n};\n// Default Configuration\nconst DEFAULT_AI_CONFIG = {\n    providers: [],\n    defaultParameters: {\n        temperature: 0.7,\n        maxTokens: 4096,\n        topP: 1,\n        frequencyPenalty: 0,\n        presencePenalty: 0\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/types/ai-config.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ea662773702b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb3ZhYmxlLXVpLy4vYXBwL2dsb2JhbHMuY3NzPzdjNTQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlYTY2Mjc3MzcwMmJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _lib_contexts_ai_config_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/contexts/ai-config-context */ \"(rsc)/./lib/contexts/ai-config-context.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"Lovable Clone - AI-Powered Code Generation\",\n    description: \"Build applications faster with AI-powered code generation\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_contexts_ai_config_context__WEBPACK_IMPORTED_MODULE_2__.AIConfigProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFLTUE7QUFIaUI7QUFDNkM7QUFJN0QsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1YsMkpBQWU7c0JBQzlCLDRFQUFDQyw2RUFBZ0JBOzBCQUNkSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG92YWJsZS11aS8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XHJcbmltcG9ydCB7IEludGVyIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcclxuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xyXG5pbXBvcnQgeyBBSUNvbmZpZ1Byb3ZpZGVyIH0gZnJvbSBcIkAvbGliL2NvbnRleHRzL2FpLWNvbmZpZy1jb250ZXh0XCI7XHJcblxyXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogW1wibGF0aW5cIl0gfSk7XHJcblxyXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xyXG4gIHRpdGxlOiBcIkxvdmFibGUgQ2xvbmUgLSBBSS1Qb3dlcmVkIENvZGUgR2VuZXJhdGlvblwiLFxyXG4gIGRlc2NyaXB0aW9uOiBcIkJ1aWxkIGFwcGxpY2F0aW9ucyBmYXN0ZXIgd2l0aCBBSS1wb3dlcmVkIGNvZGUgZ2VuZXJhdGlvblwiLFxyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IFJlYWRvbmx5PHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xyXG59Pikge1xyXG4gIHJldHVybiAoXHJcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cclxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxyXG4gICAgICAgIDxBSUNvbmZpZ1Byb3ZpZGVyPlxyXG4gICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgIDwvQUlDb25maWdQcm92aWRlcj5cclxuICAgICAgPC9ib2R5PlxyXG4gICAgPC9odG1sPlxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsiaW50ZXIiLCJBSUNvbmZpZ1Byb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/settings/page.tsx":
/*!*******************************!*\
  !*** ./app/settings/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\app\settings\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\app\settings\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./lib/contexts/ai-config-context.tsx":
/*!********************************************!*\
  !*** ./lib/contexts/ai-config-context.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AIConfigProvider: () => (/* binding */ e0),
/* harmony export */   useAIConfig: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\contexts\ai-config-context.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\contexts\ai-config-context.tsx#AIConfigProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\contexts\ai-config-context.tsx#useAIConfig`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();