import { ProviderConfig, ValidationResult, ModelParameters, AVAILABLE_MODELS } from '../types/ai-config';

/**
 * Validate provider configuration
 */
export function validateProviderConfig(provider: ProviderConfig): ValidationResult {
  const errors: string[] = [];

  // Basic validation
  if (!provider.name?.trim()) {
    errors.push('Provider name is required');
  }

  if (!provider.apiKey?.trim()) {
    errors.push('API key is required');
  }

  if (!provider.provider) {
    errors.push('Provider type is required');
  }

  // Provider-specific validation
  switch (provider.provider) {
    case 'custom':
      const customProvider = provider as any;
      if (!customProvider.baseURL?.trim()) {
        errors.push('Base URL is required for custom providers');
      } else {
        try {
          new URL(customProvider.baseURL);
        } catch {
          errors.push('Base URL must be a valid URL');
        }
      }
      
      if (!customProvider.modelName?.trim()) {
        errors.push('Model name is required for custom providers');
      }
      break;

    case 'openai':
      const openaiProvider = provider as any;
      if (openaiProvider.baseURL) {
        try {
          new URL(openaiProvider.baseURL);
        } catch {
          errors.push('Base URL must be a valid URL');
        }
      }
      break;

    case 'anthropic':
    case 'google':
    case 'cohere':
    case 'mistral':
      // Standard validation for these providers
      const standardProvider = provider as any;
      if (standardProvider.baseURL) {
        try {
          new URL(standardProvider.baseURL);
        } catch {
          errors.push('Base URL must be a valid URL');
        }
      }
      break;

    default:
      errors.push(`Unsupported provider type: ${(provider as any).provider}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Validate model parameters
 */
export function validateModelParameters(parameters: ModelParameters): ValidationResult {
  const errors: string[] = [];

  if (parameters.temperature !== undefined) {
    if (parameters.temperature < 0 || parameters.temperature > 2) {
      errors.push('Temperature must be between 0 and 2');
    }
  }

  if (parameters.maxTokens !== undefined) {
    if (parameters.maxTokens < 1 || parameters.maxTokens > 100000) {
      errors.push('Max tokens must be between 1 and 100,000');
    }
  }

  if (parameters.topP !== undefined) {
    if (parameters.topP < 0 || parameters.topP > 1) {
      errors.push('Top P must be between 0 and 1');
    }
  }

  if (parameters.topK !== undefined) {
    if (parameters.topK < 1 || parameters.topK > 100) {
      errors.push('Top K must be between 1 and 100');
    }
  }

  if (parameters.frequencyPenalty !== undefined) {
    if (parameters.frequencyPenalty < -2 || parameters.frequencyPenalty > 2) {
      errors.push('Frequency penalty must be between -2 and 2');
    }
  }

  if (parameters.presencePenalty !== undefined) {
    if (parameters.presencePenalty < -2 || parameters.presencePenalty > 2) {
      errors.push('Presence penalty must be between -2 and 2');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Validate model compatibility with provider
 */
export function validateModelForProvider(provider: ProviderConfig, modelId: string): ValidationResult {
  const errors: string[] = [];

  if (provider.provider === 'custom') {
    // For custom providers, we can't validate the model ID
    return { isValid: true, errors: [] };
  }

  const availableModels = AVAILABLE_MODELS[provider.provider] || [];
  const modelExists = availableModels.some(model => model.id === modelId);

  if (!modelExists) {
    errors.push(`Model "${modelId}" is not available for provider "${provider.provider}"`);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Validate API key format for different providers
 */
export function validateApiKeyFormat(provider: string, apiKey: string): ValidationResult {
  const errors: string[] = [];

  if (!apiKey?.trim()) {
    errors.push('API key is required');
    return { isValid: false, errors };
  }

  switch (provider) {
    case 'openai':
      if (!apiKey.startsWith('sk-')) {
        errors.push('OpenAI API key should start with "sk-"');
      }
      break;

    case 'anthropic':
      if (!apiKey.startsWith('sk-ant-')) {
        errors.push('Anthropic API key should start with "sk-ant-"');
      }
      break;

    case 'google':
      // Google API keys are typically 39 characters long
      if (apiKey.length < 20) {
        errors.push('Google API key appears to be too short');
      }
      break;

    case 'cohere':
      // Cohere API keys are typically UUIDs or similar
      if (apiKey.length < 20) {
        errors.push('Cohere API key appears to be too short');
      }
      break;

    case 'mistral':
      // Mistral API keys typically start with specific prefixes
      if (apiKey.length < 20) {
        errors.push('Mistral API key appears to be too short');
      }
      break;

    case 'custom':
      // For custom providers, we can't validate the format
      break;

    default:
      errors.push(`Unknown provider type: ${provider}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Sanitize provider configuration for safe storage
 */
export function sanitizeProviderConfig(provider: ProviderConfig): ProviderConfig {
  // Remove any potentially dangerous properties
  const sanitized = { ...provider };
  
  // Ensure required fields are present
  if (!sanitized.id) {
    sanitized.id = `${sanitized.provider}-${Date.now()}`;
  }

  // Trim string values
  sanitized.name = sanitized.name?.trim() || '';
  sanitized.apiKey = sanitized.apiKey?.trim() || '';

  // Sanitize URLs
  if ((sanitized as any).baseURL) {
    try {
      const url = new URL((sanitized as any).baseURL);
      (sanitized as any).baseURL = url.toString();
    } catch {
      delete (sanitized as any).baseURL;
    }
  }

  return sanitized;
}

/**
 * Check if provider supports specific features
 */
export function getProviderCapabilities(provider: string) {
  const capabilities = {
    streaming: true,
    tools: false,
    vision: false,
    customEndpoint: false,
  };

  switch (provider) {
    case 'openai':
      capabilities.tools = true;
      capabilities.vision = true;
      capabilities.customEndpoint = true;
      break;

    case 'anthropic':
      capabilities.tools = true;
      capabilities.vision = true;
      break;

    case 'google':
      capabilities.tools = true;
      capabilities.vision = true;
      break;

    case 'cohere':
      capabilities.tools = true;
      break;

    case 'mistral':
      capabilities.tools = true;
      break;

    case 'custom':
      capabilities.customEndpoint = true;
      // Assume basic capabilities for custom providers
      break;
  }

  return capabilities;
}
