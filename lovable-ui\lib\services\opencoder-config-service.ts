import { OpencoderService } from './opencoder-service';
import { AIConfigService } from './ai-config-service';
import { ProviderConfig, ModelParameters } from '../types/ai-config';
import type { Config } from 'opencoder';

export interface OpencoderConfigOptions {
  provider?: ProviderConfig;
  modelId?: string;
  parameters?: ModelParameters;
  systemPrompt?: string;
  toolConfirmation?: {
    enabled?: boolean;
    autoAcceptTools?: string[] | true;
    autoAcceptBashCommands?: string[] | true;
  };
  experimental?: {
    persistentChat?: boolean;
    diagnosticsTool?: boolean;
    disableDefaultGuidelines?: boolean;
    telemetry?: boolean;
    codeBaseIndex?: {
      enabled?: boolean;
    };
    autoLoad?: true | string[];
  };
}

export class OpencoderConfigService {
  private static instance: OpencoderConfigService;
  private aiConfigService: AIConfigService;
  private opencoderService: OpencoderService;

  private constructor() {
    this.aiConfigService = AIConfigService.getInstance();
    this.opencoderService = OpencoderService.getInstance();
  }

  static getInstance(): OpencoderConfigService {
    if (!OpencoderConfigService.instance) {
      OpencoderConfigService.instance = new OpencoderConfigService();
    }
    return OpencoderConfigService.instance;
  }

  /**
   * Create an opencoder configuration from the current AI settings
   */
  createOpencoderConfig(options: OpencoderConfigOptions = {}): Config {
    const {
      provider,
      modelId,
      parameters,
      systemPrompt,
      toolConfirmation,
      experimental,
    } = options;

    // Use provided provider or get default
    const finalProvider = provider || this.aiConfigService.getDefaultProvider();
    const finalModelId = modelId || this.aiConfigService.getDefaultModel();

    if (!finalProvider || !finalModelId) {
      throw new Error('No AI provider or model configured. Please configure a provider in settings.');
    }

    // Create the model using opencoder's model creation
    const model = this.createModelFromProvider(finalProvider, finalModelId);

    // Merge parameters with defaults
    const finalParameters: ModelParameters = {
      ...this.aiConfigService.getConfig().defaultParameters,
      ...parameters,
    };

    const config: Config = {
      model,
      toolConfirmation: {
        enabled: false, // Disable for API usage by default
        autoAcceptTools: true,
        autoAcceptBashCommands: true,
        ...toolConfirmation,
      },
      experimental: {
        persistentChat: false,
        diagnosticsTool: true,
        disableDefaultGuidelines: false,
        telemetry: false,
        codeBaseIndex: {
          enabled: false,
        },
        autoLoad: true,
        ...experimental,
      },
    };

    // Add system prompt with parameter guidance
    if (systemPrompt || finalParameters) {
      const systemPromptParts: string[] = [];
      
      if (systemPrompt) {
        systemPromptParts.push(systemPrompt);
      }

      // Add parameter-based guidance
      if (finalParameters.temperature !== undefined) {
        const tempGuidance = this.getTemperatureGuidance(finalParameters.temperature);
        systemPromptParts.push(tempGuidance);
      }

      if (finalParameters.maxTokens !== undefined) {
        systemPromptParts.push(`Keep responses concise, aiming for around ${finalParameters.maxTokens} tokens or less when possible.`);
      }

      if (systemPromptParts.length > 0) {
        config.system = `{{ DEFAULT_PROMPT }}\n\nAdditional guidelines:\n${systemPromptParts.join('\n')}`;
      }
    }

    return config;
  }

  /**
   * Create a model instance from a provider configuration
   */
  private createModelFromProvider(provider: ProviderConfig, modelId: string) {
    // This uses the same logic as OpencoderService but returns the model directly
    switch (provider.provider) {
      case 'openai': {
        const { openai } = require('opencoder');
        const client = openai({
          apiKey: provider.apiKey,
          baseURL: (provider as any).baseURL,
          organization: (provider as any).organization,
          project: (provider as any).project,
        });
        return client(modelId);
      }
      
      case 'anthropic': {
        const { anthropic } = require('opencoder');
        const client = anthropic({
          apiKey: provider.apiKey,
          baseURL: (provider as any).baseURL,
        });
        return client(modelId);
      }
      
      case 'google': {
        const { google } = require('opencoder');
        const client = google({
          apiKey: provider.apiKey,
          baseURL: (provider as any).baseURL,
        });
        return client(modelId);
      }
      
      case 'custom': {
        const { openai } = require('opencoder');
        const client = openai({
          apiKey: provider.apiKey,
          baseURL: (provider as any).baseURL,
        });
        return client((provider as any).modelName || modelId);
      }
      
      default:
        throw new Error(`Unsupported provider: ${provider.provider}`);
    }
  }

  /**
   * Get temperature-based guidance for the system prompt
   */
  private getTemperatureGuidance(temperature: number): string {
    if (temperature <= 0.3) {
      return 'Be very focused and deterministic in your responses. Stick to proven patterns and avoid experimental approaches.';
    } else if (temperature <= 0.7) {
      return 'Balance creativity with reliability. Use established patterns but feel free to suggest improvements.';
    } else {
      return 'Be creative and explore innovative solutions. Consider multiple approaches and suggest modern alternatives.';
    }
  }

  /**
   * Generate code using opencoder with the current configuration
   */
  async generateCode(
    prompt: string, 
    options: OpencoderConfigOptions = {}
  ) {
    const provider = options.provider || this.aiConfigService.getDefaultProvider();
    const modelId = options.modelId || this.aiConfigService.getDefaultModel();

    if (!provider || !modelId) {
      throw new Error('No AI provider or model configured. Please configure a provider in settings.');
    }

    return await this.opencoderService.generateCode({
      prompt,
      provider,
      modelId,
      parameters: options.parameters,
      systemPrompt: options.systemPrompt,
    });
  }

  /**
   * Test opencoder configuration with a simple prompt
   */
  async testConfiguration(options: OpencoderConfigOptions = {}) {
    try {
      const result = await this.generateCode(
        'Create a simple hello world function in TypeScript',
        {
          ...options,
          parameters: {
            maxTokens: 100,
            temperature: 0,
            ...options.parameters,
          },
        }
      );

      return {
        success: result.success,
        error: result.error,
        messageCount: result.messages?.length || 0,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Test failed',
        messageCount: 0,
      };
    }
  }

  /**
   * Get available providers for opencoder
   */
  getAvailableProviders(): ProviderConfig[] {
    return this.aiConfigService.getEnabledProviders();
  }

  /**
   * Get current default configuration
   */
  getCurrentConfig(): {
    provider: ProviderConfig | null;
    model: string | null;
    parameters: ModelParameters;
  } {
    return {
      provider: this.aiConfigService.getDefaultProvider(),
      model: this.aiConfigService.getDefaultModel(),
      parameters: this.aiConfigService.getConfig().defaultParameters,
    };
  }

  /**
   * Validate opencoder configuration
   */
  validateConfiguration(options: OpencoderConfigOptions): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    const provider = options.provider || this.aiConfigService.getDefaultProvider();
    const modelId = options.modelId || this.aiConfigService.getDefaultModel();

    if (!provider) {
      errors.push('No AI provider configured');
    }

    if (!modelId) {
      errors.push('No AI model configured');
    }

    if (options.parameters) {
      const { validateModelParameters } = require('../utils/ai-validation');
      const paramValidation = validateModelParameters(options.parameters);
      if (!paramValidation.isValid) {
        errors.push(...paramValidation.errors);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
